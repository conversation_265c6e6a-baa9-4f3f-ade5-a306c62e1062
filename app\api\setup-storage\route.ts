import { NextRequest, NextResponse } from 'next/server'
import { createServerClient } from '@/lib/supabase'

export async function POST(request: NextRequest) {
  try {
    const supabase = createServerClient()

    // Check if user is admin
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Create storage bucket for nail images
    const { data: bucket, error: bucketError } = await supabase.storage.createBucket('nail-images', {
      public: true,
      allowedMimeTypes: ['image/jpeg', 'image/png', 'image/webp'],
      fileSizeLimit: 5242880 // 5MB
    })

    if (bucketError && !bucketError.message.includes('already exists')) {
      console.error('Error creating bucket:', bucketError)
      return NextResponse.json({ error: 'Failed to create storage bucket', details: bucketError }, { status: 500 })
    }

    // Set up storage policies
    const policies = [
      {
        name: 'Anyone can view nail images',
        definition: `bucket_id = 'nail-images'`,
        command: 'SELECT'
      },
      {
        name: 'Authenticated users can upload nail images',
        definition: `bucket_id = 'nail-images' AND auth.role() = 'authenticated'`,
        command: 'INSERT'
      },
      {
        name: 'Users can update their own nail images',
        definition: `bucket_id = 'nail-images' AND auth.uid()::text = (storage.foldername(name))[1]`,
        command: 'UPDATE'
      },
      {
        name: 'Users can delete their own nail images',
        definition: `bucket_id = 'nail-images' AND auth.uid()::text = (storage.foldername(name))[1]`,
        command: 'DELETE'
      }
    ]

    // Apply storage policies
    for (const policy of policies) {
      const { error: policyError } = await supabase.rpc('create_storage_policy', {
        policy_name: policy.name,
        bucket_name: 'nail-images',
        definition: policy.definition,
        command: policy.command
      })

      if (policyError && !policyError.message.includes('already exists')) {
        console.error('Error creating storage policy:', policyError)
      }
    }

    return NextResponse.json({ 
      success: true, 
      message: 'Storage bucket and policies created successfully',
      bucket: bucket || 'Already exists'
    })

  } catch (error) {
    console.error('Storage setup error:', error)
    return NextResponse.json({ error: 'Storage setup failed', details: error }, { status: 500 })
  }
}
