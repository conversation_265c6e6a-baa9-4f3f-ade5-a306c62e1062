"use client"

import { useState, useEffect } from 'react'
import { Card, CardContent } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Heart, Eye, Calendar, User, ChevronRight } from 'lucide-react'
import { supabase } from '@/lib/supabase'
import { useAuth } from '@/components/auth/auth-provider'
import { toast } from 'sonner'
import Link from 'next/link'

interface NailImage {
  id: string
  image_url: string
  likes_count: number
  created_at: string
  is_featured: boolean
  user: {
    first_name: string
    last_name: string
  }
  appointment: {
    appointment_date: string
    services: { name: string }[]
  }
  user_has_liked?: boolean
}

interface MyWorkGalleryProps {
  showTitle?: boolean
  limit?: number
  showViewAll?: boolean
}

export function MyWorkGallery({ showTitle = true, limit, showViewAll = true }: MyWorkGalleryProps) {
  const [images, setImages] = useState<NailImage[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const { user, isSignedIn } = useAuth()

  useEffect(() => {
    fetchImages()
  }, [limit])

  const fetchImages = async () => {
    setLoading(true)
    setError(null)
    try {
      let query = supabase
        .from('nail_images')
        .select(`
          id,
          image_url,
          likes_count,
          created_at,
          is_featured,
          user:users!nail_images_user_id_fkey (
            first_name,
            last_name
          ),
          appointment:appointments!nail_images_appointment_id_fkey (
            appointment_date,
            services:service_ids (
              name
            )
          )
        `)
        .eq('is_private', false)
        .order('created_at', { ascending: false })

      if (limit) {
        query = query.limit(limit)
      }

      const { data, error } = await query

      if (error) {
        console.error('Error fetching images:', error)
        setError('Failed to load gallery images. Please try again later.')
        toast.error('Failed to load images')
        return
      }

      // Check if user has liked each image
      if (isSignedIn && user) {
        const imageIds = data?.map(img => img.id) || []
        const { data: likes } = await supabase
          .from('image_likes')
          .select('image_id')
          .eq('user_id', user.id)
          .in('image_id', imageIds)

        const likedImageIds = new Set(likes?.map(like => like.image_id) || [])
        
        // Transform the data to handle the relationship arrays correctly
        const transformedData = (data || []).map(img => ({
          ...img,
          user: Array.isArray(img.user) ? img.user[0] : img.user,
          appointment: Array.isArray(img.appointment) ? img.appointment[0] : img.appointment,
          user_has_liked: likedImageIds.has(img.id)
        }))

        setImages(transformedData)
      } else {
        // Transform the data to handle the relationship arrays correctly
        const transformedData = (data || []).map(img => ({
          ...img,
          user: Array.isArray(img.user) ? img.user[0] : img.user,
          appointment: Array.isArray(img.appointment) ? img.appointment[0] : img.appointment
        }))

        setImages(transformedData)
      }
    } catch (error) {
      console.error('Error fetching images:', error)
      setError('Failed to load gallery images. Please try again later.')
      toast.error('Failed to load images')
    } finally {
      setLoading(false)
    }
  }

  const handleLike = async (imageId: string, currentlyLiked: boolean) => {
    if (!isSignedIn || !user) {
      toast.error('Please sign in to like images')
      return
    }

    try {
      if (currentlyLiked) {
        // Unlike
        const { error } = await supabase
          .from('image_likes')
          .delete()
          .eq('user_id', user.id)
          .eq('image_id', imageId)

        if (error) {
          console.error('Error unliking image:', error)
          toast.error('Failed to unlike image')
          return
        }

        // Update likes count
        const { error: updateError } = await supabase.rpc('decrement_likes_count', {
          image_id: imageId
        })

        if (updateError) {
          console.error('Error updating likes count:', updateError)
        }
      } else {
        // Like
        const { error } = await supabase
          .from('image_likes')
          .insert({
            user_id: user.id,
            image_id: imageId
          })

        if (error) {
          console.error('Error liking image:', error)
          toast.error('Failed to like image')
          return
        }

        // Update likes count
        const { error: updateError } = await supabase.rpc('increment_likes_count', {
          image_id: imageId
        })

        if (updateError) {
          console.error('Error updating likes count:', updateError)
        }
      }

      // Update local state
      setImages(prev => prev.map(img => 
        img.id === imageId 
          ? { 
              ...img, 
              user_has_liked: !currentlyLiked,
              likes_count: currentlyLiked ? img.likes_count - 1 : img.likes_count + 1
            }
          : img
      ))

      toast.success(currentlyLiked ? 'Image unliked' : 'Image liked!')
    } catch (error) {
      console.error('Error toggling like:', error)
      toast.error('Failed to update like')
    }
  }

  if (loading) {
    return (
      <div className="space-y-4">
        {showTitle && <h2 className="text-xl font-bold text-gray-900">My Work</h2>}
        <div className="grid grid-cols-2 gap-3">
          {[...Array(4)].map((_, i) => (
            <div key={i} className="aspect-square bg-gray-200 rounded-lg animate-pulse" />
          ))}
        </div>
      </div>
    )
  }

  if (images.length === 0) {
    return (
      <div className="space-y-4">
        {showTitle && <h2 className="text-xl font-bold text-gray-900">My Work</h2>}
        <Card className="border-pink-200 bg-white/80 backdrop-blur-sm">
          <CardContent className="p-6 text-center">
            <Eye className="h-12 w-12 text-pink-300 mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-pink-800 mb-2">No Photos Yet</h3>
            <p className="text-pink-600 mb-4">Be the first to share your beautiful nail art!</p>
            {!isSignedIn && (
              <p className="text-sm text-gray-500">Sign in to upload and like photos</p>
            )}
          </CardContent>
        </Card>
      </div>
    )
  }

  if (error) {
    return (
      <div className="space-y-4">
        {showTitle && (
          <h2 className="text-xl font-bold text-gray-900">My Work</h2>
        )}
        <Card className="border-red-200 bg-red-50">
          <CardContent className="p-4 text-center">
            <p className="text-red-700 text-sm mb-2">{error}</p>
            <Button
              size="sm"
              variant="outline"
              onClick={fetchImages}
              className="border-red-200 text-red-700 hover:bg-red-100"
            >
              Try Again
            </Button>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="space-y-4">
      {showTitle && (
        <div className="flex items-center justify-between">
          <h2 className="text-xl font-bold text-gray-900">My Work</h2>
          {showViewAll && images.length > 0 && (
            <Button variant="ghost" size="sm" className="text-pink-600" asChild>
              <Link href="/gallery">
                View All
                <ChevronRight className="ml-1 h-4 w-4" />
              </Link>
            </Button>
          )}
        </div>
      )}

      <div className="grid grid-cols-2 gap-3">
        {images.map((image) => (
          <Card key={image.id} className="border-0 shadow-sm overflow-hidden group">
            <div className="relative aspect-square">
              <img 
                src={image.image_url} 
                alt="Nail art" 
                className="w-full h-full object-cover transition-transform group-hover:scale-105"
              />
              
              {/* Overlay */}
              <div className="absolute inset-0 bg-gradient-to-t from-black/70 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity">
                <div className="absolute bottom-0 left-0 right-0 p-3">
                  <div className="flex items-center justify-between text-white text-sm">
                    <div className="flex items-center space-x-2">
                      <User className="h-3 w-3" />
                      <span>{image.user.first_name} {image.user.last_name}</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Calendar className="h-3 w-3" />
                      <span>{new Date(image.appointment.appointment_date).toLocaleDateString()}</span>
                    </div>
                  </div>
                </div>
              </div>

              {/* Featured badge */}
              {image.is_featured && (
                <Badge className="absolute top-2 left-2 bg-amber-500 text-white">
                  Featured
                </Badge>
              )}

              {/* Like button */}
              <Button
                size="sm"
                variant="ghost"
                onClick={() => handleLike(image.id, image.user_has_liked || false)}
                className="absolute top-2 right-2 bg-white/80 hover:bg-white text-pink-600 h-8 w-8 p-0"
              >
                <Heart 
                  className={`h-4 w-4 ${image.user_has_liked ? 'fill-current' : ''}`} 
                />
              </Button>
            </div>

            <CardContent className="p-3">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-1 text-pink-600">
                  <Heart className="h-4 w-4" />
                  <span className="text-sm font-medium">{image.likes_count}</span>
                </div>
                <div className="text-xs text-gray-500">
                  {image.appointment.services?.map(s => s.name).join(', ')}
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {!isSignedIn && (
        <Card className="border-pink-200 bg-pink-50">
          <CardContent className="p-4 text-center">
            <p className="text-pink-700 text-sm mb-2">Love what you see?</p>
            <Button size="sm" className="bg-gradient-to-r from-pink-500 to-amber-500 hover:from-pink-600 hover:to-amber-600">
              Sign In to Like & Save
            </Button>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
