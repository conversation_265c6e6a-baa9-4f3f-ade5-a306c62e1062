"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { App<PERSON>eader } from "@/components/ui/app-header"
import { Gift, Users, Mail, CheckCircle, Copy, Share } from "lucide-react"

export default function ReferralsPage() {
  const [emails, setEmails] = useState([""])
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [submitted, setSubmitted] = useState(false)
  const [referralCode] = useState("LUXE2024") // In real app, this would be user-specific

  const addEmailField = () => {
    if (emails.length < 5) {
      setEmails([...emails, ""])
    }
  }

  const updateEmail = (index: number, value: string) => {
    const newEmails = [...emails]
    newEmails[index] = value
    setEmails(newEmails)
  }

  const removeEmail = (index: number) => {
    if (emails.length > 1) {
      const newEmails = emails.filter((_, i) => i !== index)
      setEmails(newEmails)
    }
  }

  const handleSubmit = async () => {
    const validEmails = emails.filter((email) => email.trim() && email.includes("@"))
    if (validEmails.length === 0) return

    setIsSubmitting(true)
    // Simulate API call
    await new Promise((resolve) => setTimeout(resolve, 2000))
    setIsSubmitting(false)
    setSubmitted(true)
  }

  const copyReferralCode = () => {
    navigator.clipboard.writeText(referralCode)
    // In real app, show toast notification
  }

  const shareReferral = () => {
    if (navigator.share) {
      navigator.share({
        title: "Nails by Lingg",
        text: `Check out Nails by Lingg! Use my referral code ${referralCode} for 20% off your first appointment.`,
        url: window.location.origin,
      })
    }
  }

  if (submitted) {
    return (
      <div className="min-h-screen bg-gray-50 pb-20">
        <AppHeader title="Referrals Sent!" showBack />

        <div className="p-4 flex items-center justify-center min-h-[70vh]">
          <Card className="border-0 shadow-lg text-center max-w-sm">
            <CardContent className="p-8">
              <div className="w-20 h-20 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-6">
                <CheckCircle className="h-10 w-10 text-green-600" />
              </div>

              <h2 className="text-2xl font-bold text-gray-900 mb-4">Invitations Sent!</h2>
              <p className="text-gray-600 mb-6">
                Your friends will receive an email invitation with a special 20% off discount. You'll earn rewards when
                they book their first appointment!
              </p>

              <div className="bg-pink-50 p-4 rounded-lg mb-6 border border-pink-200">
                <p className="text-pink-700 font-semibold">You'll earn $10 credit for each friend who books!</p>
              </div>

              <Button
                onClick={() => setSubmitted(false)}
                className="w-full bg-gradient-to-r from-pink-500 to-amber-500 hover:from-pink-600 hover:to-amber-600 mb-3"
              >
                Refer More Friends
              </Button>
              <Button variant="outline" onClick={() => window.history.back()} className="w-full border-pink-200">
                Back to App
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50 pb-20">
      <AppHeader title="Refer Friends" showBack />

      <div className="p-4 space-y-6">
        {/* Rewards Info */}
        <Card className="border-0 shadow-sm bg-gradient-to-r from-pink-500 to-amber-500 text-white">
          <CardContent className="p-6 text-center">
            <Gift className="h-12 w-12 mx-auto mb-4" />
            <h2 className="text-xl font-bold mb-2">Earn $10 Per Referral!</h2>
            <p className="text-pink-100 mb-4">
              Share the luxury with friends and earn $10 credit for each friend who books their first appointment.
            </p>
            <div className="bg-white/20 rounded-lg p-3">
              <p className="text-sm">Your friends get 20% off their first visit!</p>
            </div>
          </CardContent>
        </Card>

        {/* Your Referral Code */}
        <Card className="border-0 shadow-sm">
          <CardContent className="p-4">
            <h3 className="font-semibold text-gray-900 mb-3">Your Referral Code</h3>
            <div className="flex items-center space-x-2">
              <div className="flex-1 bg-gray-100 p-3 rounded-lg">
                <code className="text-lg font-bold text-pink-600">{referralCode}</code>
              </div>
              <Button variant="outline" size="sm" onClick={copyReferralCode} className="border-pink-200">
                <Copy className="h-4 w-4" />
              </Button>
              <Button variant="outline" size="sm" onClick={shareReferral} className="border-pink-200">
                <Share className="h-4 w-4" />
              </Button>
            </div>
            <p className="text-sm text-gray-500 mt-2">Share this code or use the form below to send invitations</p>
          </CardContent>
        </Card>

        {/* Email Invitations */}
        <Card className="border-0 shadow-sm">
          <CardContent className="p-4 space-y-4">
            <div className="flex items-center justify-between">
              <h3 className="font-semibold text-gray-900">Send Email Invitations</h3>
              <Users className="h-5 w-5 text-pink-500" />
            </div>

            <div className="space-y-3">
              {emails.map((email, index) => (
                <div key={index} className="flex items-center space-x-2">
                  <div className="flex-1">
                    <Label htmlFor={`email-${index}`} className="sr-only">
                      Email {index + 1}
                    </Label>
                    <Input
                      id={`email-${index}`}
                      type="email"
                      placeholder="<EMAIL>"
                      value={email}
                      onChange={(e) => updateEmail(index, e.target.value)}
                      className="border-pink-200 focus:border-pink-500"
                    />
                  </div>
                  {emails.length > 1 && (
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => removeEmail(index)}
                      className="text-gray-400 hover:text-red-500"
                    >
                      ×
                    </Button>
                  )}
                </div>
              ))}
            </div>

            {emails.length < 5 && (
              <Button variant="outline" onClick={addEmailField} className="w-full border-pink-200 text-pink-700">
                + Add Another Email
              </Button>
            )}

            <div className="bg-pink-50 p-3 rounded-lg border border-pink-200">
              <p className="text-sm text-pink-700">
                <Mail className="h-4 w-4 inline mr-1" />
                We'll send a beautiful invitation email with your personal referral code and their 20% discount.
              </p>
            </div>
          </CardContent>
        </Card>

        {/* Current Referrals */}
        <Card className="border-0 shadow-sm">
          <CardContent className="p-4">
            <h3 className="font-semibold text-gray-900 mb-3">Your Referral Stats</h3>
            <div className="grid grid-cols-3 gap-4 text-center">
              <div>
                <div className="text-2xl font-bold text-pink-600">3</div>
                <div className="text-xs text-gray-500">Invited</div>
              </div>
              <div>
                <div className="text-2xl font-bold text-green-600">1</div>
                <div className="text-xs text-gray-500">Booked</div>
              </div>
              <div>
                <div className="text-2xl font-bold text-amber-600">$10</div>
                <div className="text-xs text-gray-500">Earned</div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Send Button */}
        <Button
          onClick={handleSubmit}
          disabled={isSubmitting || emails.every((email) => !email.trim())}
          className="w-full py-3 bg-gradient-to-r from-pink-500 to-amber-500 hover:from-pink-600 hover:to-amber-600 disabled:opacity-50"
        >
          {isSubmitting ? (
            <div className="flex items-center">
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
              Sending Invitations...
            </div>
          ) : (
            <>
              <Mail className="mr-2 h-4 w-4" />
              Send Invitations
            </>
          )}
        </Button>
      </div>
    </div>
  )
}
