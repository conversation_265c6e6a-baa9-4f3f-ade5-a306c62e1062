"use client"

import type React from "react"

import { useState } from "react"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Badge } from "@/components/ui/badge"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Calendar, Users, DollarSign, Camera, Upload, Star, Clock, Menu, User, FileText } from "lucide-react"
import { Textarea } from "@/components/ui/textarea"
import { AdminSidebar, AdminSidebarProvider } from "@/components/admin/admin-sidebar"
import { AdminAnnouncements } from "@/components/admin/announcements"
import { AdminHomepageStats } from "@/components/admin/homepage-stats"
import { AdminCustomerReviews } from "@/components/admin/customer-reviews"
import { AdminBusinessInfo } from "@/components/admin/business-info"
import { AdminServiceCombinations } from "@/components/admin/service-combinations"
import { useAuth } from "@/components/auth/auth-provider"
import { SidebarInset, SidebarTrigger } from "@/components/ui/sidebar"

// Navigation items for the admin sidebar (copied from admin-sidebar.tsx)
const adminNavItems = [
  {
    title: "Overview",
    items: [
      {
        title: "Dashboard",
        value: "dashboard",
      },
      {
        title: "Appointments",
        value: "appointments",
      },
    ],
  },
  {
    title: "Business Management",
    items: [
      {
        title: "Availability",
        value: "availability",
      },
      {
        title: "Services",
        value: "services",
      },
    ],
  },
  {
    title: "Customer Management",
    items: [
      {
        title: "Clients",
        value: "users",
      },
      {
        title: "Portfolio",
        value: "portfolio",
      },
      {
        title: "Referrals",
        value: "referrals",
      },
      {
        title: "Announcements",
        value: "announcements",
      },
    ],
  },
]

// Type definitions
interface Appointment {
  id: number;
  client: string;
  services: string[];
  date: string;
  time: string;
  duration: number;
  status: string;
  price: number;
}

interface Service {
  id: string;
  name: string;
  price: number;
  duration: number;
  category: string;
  description: string;
  image: string;
  active: boolean;
}

interface BlockedDate {
  id?: number;
  date: string;
  reason: string;
  type: "full-day" | "partial";
  timeSlots?: string[];
}

interface NewService {
  name: string;
  price: string;
  duration: string;
  category: string;
  description: string;
  image: string;
  active: boolean;
}

interface NewPhoto {
  clientId: string;
  services: string[];
  featured: boolean;
}

// Updated mock data with multiple services
const appointments: Appointment[] = [
  {
    id: 1,
    client: "Sarah Johnson",
    services: ["Gel Manicure", "Simple Nail Art"],
    date: "2024-01-15",
    time: "10:00 AM",
    duration: 120,
    status: "confirmed",
    price: 100,
  },
  {
    id: 2,
    client: "Emily Davis",
    services: ["Luxury Spa Manicure", "Spa Pedicure"],
    date: "2024-01-15",
    time: "2:00 PM",
    duration: 145,
    status: "pending",
    price: 125,
  },
  {
    id: 3,
    client: "Jessica Wilson",
    services: ["Diamond Manicure"],
    date: "2024-01-16",
    time: "11:00 AM",
    duration: 90,
    status: "completed",
    price: 120,
  },
]

const users = [
  { id: 1, name: "Sarah Johnson", email: "<EMAIL>", phone: "(*************", member: true, totalSpent: 450 },
  { id: 2, name: "Emily Davis", email: "<EMAIL>", phone: "(*************", member: false, totalSpent: 65 },
  { id: 3, name: "Jessica Wilson", email: "<EMAIL>", phone: "(*************", member: true, totalSpent: 890 },
]

const portfolioPhotos = [
  {
    id: 1,
    url: "/placeholder.svg?height=200&width=200&text=Gel+Manicure",
    clientName: "Sarah Johnson",
    service: "Gel Manicure",
    date: "2024-01-15",
    featured: true,
    approved: true,
  },
  {
    id: 2,
    url: "/placeholder.svg?height=200&width=200&text=Nail+Art",
    clientName: "Emily Davis",
    service: "Intricate Nail Art",
    date: "2024-01-14",
    featured: false,
    approved: true,
  },
  {
    id: 3,
    url: "/placeholder.svg?height=200&width=200&text=Diamond+Luxury",
    clientName: "Jessica Wilson",
    service: "Diamond Manicure",
    date: "2024-01-13",
    featured: true,
    approved: false,
  },
]

// Service management data
const initialServices: Service[] = [
  {
    id: "classic-manicure",
    name: "Classic Manicure",
    price: 35,
    duration: 45,
    category: "Classic",
    description: "Traditional nail care with cuticle work, shaping, and polish application",
    image: "/placeholder.svg?height=200&width=300&text=Classic+Manicure",
    active: true,
  },
  {
    id: "gel-manicure",
    name: "Gel Manicure",
    price: 45,
    duration: 60,
    category: "Classic",
    description: "Long-lasting gel polish application with base and top coat",
    image: "/placeholder.svg?height=200&width=300&text=Gel+Manicure",
    active: true,
  },
  {
    id: "luxury-spa",
    name: "Luxury Spa Manicure",
    price: 65,
    duration: 75,
    category: "Classic",
    description: "Premium spa experience with exfoliation, massage, and luxury treatments",
    image: "/placeholder.svg?height=200&width=300&text=Luxury+Spa",
    active: true,
  },
  {
    id: "simple-art",
    name: "Simple Nail Art",
    price: 55,
    duration: 60,
    category: "Art",
    description: "Basic nail art designs including stripes, dots, and simple patterns",
    image: "/placeholder.svg?height=200&width=300&text=Simple+Art",
    active: true,
  },
  {
    id: "intricate-art",
    name: "Intricate Nail Art",
    price: 75,
    duration: 90,
    category: "Art",
    description: "Complex artistic designs with detailed patterns and multiple colors",
    image: "/placeholder.svg?height=200&width=300&text=Intricate+Art",
    active: true,
  },
  {
    id: "gel-extensions",
    name: "Gel Extensions",
    price: 70,
    duration: 90,
    category: "Extensions",
    description: "Gel nail extensions for length and strength",
    image: "/placeholder.svg?height=200&width=300&text=Gel+Extensions",
    active: true,
  },
  {
    id: "diamond-manicure",
    name: "Diamond Manicure",
    price: 120,
    duration: 90,
    category: "Luxury",
    description: "Ultimate luxury experience with premium products and diamond dust finish",
    image: "/placeholder.svg?height=200&width=300&text=Diamond+Manicure",
    active: true,
  },
  {
    id: "classic-pedicure",
    name: "Classic Pedicure",
    price: 40,
    duration: 50,
    category: "Pedicure",
    description: "Complete foot care with soak, exfoliation, and polish",
    image: "/placeholder.svg?height=200&width=300&text=Classic+Pedicure",
    active: true,
  },
  {
    id: "spa-pedicure",
    name: "Spa Pedicure",
    price: 60,
    duration: 70,
    category: "Pedicure",
    description: "Luxury pedicure with extended massage and premium treatments",
    image: "/placeholder.svg?height=200&width=300&text=Spa+Pedicure",
    active: true,
  },
]

export default function AdminPage() {
  const { user, isSignedIn } = useAuth()
  const [services, setServices] = useState<Service[]>(initialServices)
  const [editingService, setEditingService] = useState<Service | null>(null)
  const [newService, setNewService] = useState<NewService>({
    name: "",
    price: "",
    duration: "",
    category: "",
    description: "",
    image: "",
    active: true,
  })
  const [selectedTab, setSelectedTab] = useState("dashboard")
  const [newPhoto, setNewPhoto] = useState<NewPhoto>({
    clientId: "",
    services: [],
    featured: false,
  })
  const [uploadedFile, setUploadedFile] = useState<File | null>(null)

  // Check if user is admin
  if (!isSignedIn || !user?.is_admin) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <Card className="border-pink-200 bg-white/80 backdrop-blur-sm max-w-md w-full">
          <CardContent className="p-6 text-center">
            <Users className="h-12 w-12 text-pink-300 mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-pink-800 mb-2">Admin Access Required</h3>
            <p className="text-pink-600 mb-4">
              {!isSignedIn
                ? "Please sign in with an admin account to access the admin dashboard."
                : "You don't have admin privileges to access this page."
              }
            </p>
            <Button className="bg-gradient-to-r from-pink-500 to-amber-500 hover:from-pink-600 hover:to-amber-600">
              {!isSignedIn ? "Sign In" : "Go Back"}
            </Button>
          </CardContent>
        </Card>
      </div>
    )
  }

  // Business hours and blocked dates state
  const [businessHours, setBusinessHours] = useState({
    monday: { open: "09:00", close: "19:00", closed: false },
    tuesday: { open: "09:00", close: "19:00", closed: false },
    wednesday: { open: "09:00", close: "19:00", closed: false },
    thursday: { open: "09:00", close: "19:00", closed: false },
    friday: { open: "09:00", close: "19:00", closed: false },
    saturday: { open: "09:00", close: "19:00", closed: false },
    sunday: { open: "10:00", close: "17:00", closed: true },
  })

  const [blockedDates, setBlockedDates] = useState<BlockedDate[]>([
    { date: "2024-01-20", reason: "Personal Day", type: "full-day" },
    { date: "2024-01-25", reason: "Training", type: "full-day" },
    { date: "2024-02-14", reason: "Valentine's Day - Busy", type: "partial", timeSlots: ["10:00 AM", "11:00 AM"] },
  ])

  const [newBlockedDate, setNewBlockedDate] = useState<Omit<BlockedDate, 'id'>>({
    date: "",
    reason: "",
    type: "full-day",
    timeSlots: [],
  })

  const totalRevenue = appointments.reduce((sum, apt) => sum + apt.price, 0)
  const totalClients = users.length
  const totalAppointments = appointments.length

  // Helper functions
  const handleCancelAppointment = (appointmentId: number) => {
    if (confirm("Are you sure you want to cancel this appointment?")) {
      console.log("Cancelling appointment:", appointmentId)
    }
  }

  const addBlockedDate = () => {
    if (newBlockedDate.date && newBlockedDate.reason) {
      setBlockedDates([...blockedDates, { ...newBlockedDate, id: Date.now() }])
      setNewBlockedDate({ date: "", reason: "", type: "full-day", timeSlots: [] })
    }
  }

  const removeBlockedDate = (index: number) => {
    setBlockedDates(blockedDates.filter((_, i) => i !== index))
  }

  const handlePhotoUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (file) {
      setUploadedFile(file)
    }
  }

  const handleAddPhoto = () => {
    console.log("Adding photo:", { newPhoto, uploadedFile })
    setNewPhoto({ clientId: "", services: [], featured: false })
    setUploadedFile(null)
  }

  const toggleFeatured = (photoId: number) => {
    console.log("Toggling featured status for photo:", photoId)
  }

  const toggleApproved = (photoId: number) => {
    console.log("Toggling approved status for photo:", photoId)
  }

  // Get current tab title for header
  const getCurrentTabTitle = () => {
    const allItems = adminNavItems.flatMap((group) => group.items)
    const currentItem = allItems.find((item) => item.value === selectedTab)
    return currentItem?.title || "Dashboard"
  }

  return (
    <AdminSidebarProvider>
      <div className="flex min-h-screen bg-gradient-to-br from-rose-50 to-pink-100">
        <AdminSidebar selectedTab={selectedTab} onTabChange={setSelectedTab} />

        <SidebarInset className="flex-1">
          {/* Header */}
          <header className="sticky top-0 bg-white border-b border-pink-200 z-50 shadow-sm">
            <div className="flex items-center gap-2 px-4 py-3">
              <SidebarTrigger className="text-pink-700" />
              <div className="flex items-center gap-2">
                <h1 className="text-xl font-semibold text-pink-800">{getCurrentTabTitle()}</h1>
              </div>
            </div>
          </header>

          {/* Main Content */}
          <div className="p-6 space-y-6">
            {/* Dashboard Tab */}
            {selectedTab === "dashboard" && (
              <div className="space-y-6">
                <div className="grid md:grid-cols-3 gap-6">
                  <Card className="border-pink-200 bg-white/80 backdrop-blur-sm">
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                      <CardTitle className="text-sm font-medium text-pink-800">Total Revenue</CardTitle>
                      <DollarSign className="h-4 w-4 text-amber-500" />
                    </CardHeader>
                    <CardContent>
                      <div className="text-2xl font-bold text-pink-800">${totalRevenue}</div>
                      <p className="text-xs text-pink-600">+12% from last month</p>
                    </CardContent>
                  </Card>
                  <Card className="border-pink-200 bg-white/80 backdrop-blur-sm">
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                      <CardTitle className="text-sm font-medium text-pink-800">Total Clients</CardTitle>
                      <Users className="h-4 w-4 text-amber-500" />
                    </CardHeader>
                    <CardContent>
                      <div className="text-2xl font-bold text-pink-800">{totalClients}</div>
                      <p className="text-xs text-pink-600">+3 new this week</p>
                    </CardContent>
                  </Card>
                  <Card className="border-pink-200 bg-white/80 backdrop-blur-sm">
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                      <CardTitle className="text-sm font-medium text-pink-800">Appointments</CardTitle>
                      <Calendar className="h-4 w-4 text-amber-500" />
                    </CardHeader>
                    <CardContent>
                      <div className="text-2xl font-bold text-pink-800">{totalAppointments}</div>
                      <p className="text-xs text-pink-600">This week</p>
                    </CardContent>
                  </Card>
                </div>

                <Card className="border-pink-200 bg-white/80 backdrop-blur-sm">
                  <CardHeader>
                    <CardTitle className="text-pink-800">Recent Appointments</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="overflow-x-auto">
                      <Table>
                        <TableHeader>
                          <TableRow>
                            <TableHead className="text-pink-700">Client</TableHead>
                            <TableHead className="text-pink-700">Services</TableHead>
                            <TableHead className="text-pink-700">Date</TableHead>
                            <TableHead className="text-pink-700">Duration</TableHead>
                            <TableHead className="text-pink-700">Status</TableHead>
                            <TableHead className="text-pink-700">Amount</TableHead>
                          </TableRow>
                        </TableHeader>
                        <TableBody>
                          {appointments.slice(0, 5).map((appointment) => (
                            <TableRow key={appointment.id}>
                              <TableCell className="text-pink-800">{appointment.client}</TableCell>
                              <TableCell className="text-pink-700">
                                <div className="space-y-1">
                                  {appointment.services.map((service, index) => (
                                    <Badge key={index} variant="secondary" className="bg-pink-100 text-pink-700 mr-1">
                                      {service}
                                    </Badge>
                                  ))}
                                </div>
                              </TableCell>
                              <TableCell className="text-pink-700">{appointment.date}</TableCell>
                              <TableCell className="text-pink-700">
                                <div className="flex items-center">
                                  <Clock className="h-4 w-4 mr-1" />
                                  {Math.floor(appointment.duration / 60)}h {appointment.duration % 60}m
                                </div>
                              </TableCell>
                              <TableCell>
                                <Badge
                                  variant={
                                    appointment.status === "completed"
                                      ? "default"
                                      : appointment.status === "confirmed"
                                        ? "secondary"
                                        : "outline"
                                  }
                                >
                                  {appointment.status}
                                </Badge>
                              </TableCell>
                              <TableCell className="text-pink-800 font-semibold">${appointment.price}</TableCell>
                            </TableRow>
                          ))}
                        </TableBody>
                      </Table>
                    </div>
                  </CardContent>
                </Card>
              </div>
            )}

            {/* Appointments Tab */}
            {selectedTab === "appointments" && (
              <Card className="border-pink-200 bg-white/80 backdrop-blur-sm">
                <CardHeader>
                  <CardTitle className="text-pink-800">All Appointments</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="overflow-x-auto">
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead className="text-pink-700">Client</TableHead>
                          <TableHead className="text-pink-700">Services</TableHead>
                          <TableHead className="text-pink-700">Date</TableHead>
                          <TableHead className="text-pink-700">Time</TableHead>
                          <TableHead className="text-pink-700">Duration</TableHead>
                          <TableHead className="text-pink-700">Status</TableHead>
                          <TableHead className="text-pink-700">Amount</TableHead>
                          <TableHead className="text-pink-700">Actions</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {appointments.map((appointment) => (
                          <TableRow key={appointment.id}>
                            <TableCell className="text-pink-800">{appointment.client}</TableCell>
                            <TableCell className="text-pink-700">
                              <div className="space-y-1">
                                {appointment.services.map((service, index) => (
                                  <div key={index} className="text-sm">
                                    {service}
                                  </div>
                                ))}
                              </div>
                            </TableCell>
                            <TableCell className="text-pink-700">{appointment.date}</TableCell>
                            <TableCell className="text-pink-700">{appointment.time}</TableCell>
                            <TableCell className="text-pink-700">
                              <div className="flex items-center">
                                <Clock className="h-4 w-4 mr-1" />
                                {Math.floor(appointment.duration / 60)}h {appointment.duration % 60}m
                              </div>
                            </TableCell>
                            <TableCell>
                              <Select defaultValue={appointment.status}>
                                <SelectTrigger className="w-32">
                                  <SelectValue />
                                </SelectTrigger>
                                <SelectContent>
                                  <SelectItem value="pending">Pending</SelectItem>
                                  <SelectItem value="confirmed">Confirmed</SelectItem>
                                  <SelectItem value="completed">Completed</SelectItem>
                                  <SelectItem value="cancelled">Cancelled</SelectItem>
                                  <SelectItem value="no-show">No Show</SelectItem>
                                </SelectContent>
                              </Select>
                            </TableCell>
                            <TableCell className="text-pink-800 font-semibold">${appointment.price}</TableCell>
                            <TableCell>
                              <div className="flex space-x-2">
                                <Dialog>
                                  <DialogTrigger asChild>
                                    <Button size="sm" variant="outline" className="border-pink-300 text-pink-700">
                                      View
                                    </Button>
                                  </DialogTrigger>
                                  <DialogContent className="bg-white max-w-md">
                                    <DialogHeader>
                                      <DialogTitle className="text-pink-800">Appointment Details</DialogTitle>
                                    </DialogHeader>
                                    <div className="space-y-4">
                                      <div>
                                        <Label className="text-pink-800 font-semibold">Client</Label>
                                        <p className="text-gray-700">{appointment.client}</p>
                                      </div>
                                      <div>
                                        <Label className="text-pink-800 font-semibold">Services</Label>
                                        <div className="space-y-1">
                                          {appointment.services.map((service, index) => (
                                            <Badge
                                              key={index}
                                              variant="secondary"
                                              className="bg-pink-100 text-pink-700 mr-1"
                                            >
                                              {service}
                                            </Badge>
                                          ))}
                                        </div>
                                      </div>
                                      <div className="grid grid-cols-2 gap-4">
                                        <div>
                                          <Label className="text-pink-800 font-semibold">Date</Label>
                                          <p className="text-gray-700">{appointment.date}</p>
                                        </div>
                                        <div>
                                          <Label className="text-pink-800 font-semibold">Time</Label>
                                          <p className="text-gray-700">{appointment.time}</p>
                                        </div>
                                      </div>
                                      <div className="grid grid-cols-2 gap-4">
                                        <div>
                                          <Label className="text-pink-800 font-semibold">Duration</Label>
                                          <p className="text-gray-700">
                                            {Math.floor(appointment.duration / 60)}h {appointment.duration % 60}m
                                          </p>
                                        </div>
                                        <div>
                                          <Label className="text-pink-800 font-semibold">Amount</Label>
                                          <p className="text-gray-700 font-semibold">${appointment.price}</p>
                                        </div>
                                      </div>
                                      <div className="flex space-x-2 pt-4">
                                        <Button
                                          onClick={() => handleCancelAppointment(appointment.id)}
                                          className="flex-1 bg-red-500 hover:bg-red-600 text-white"
                                          disabled={
                                            appointment.status === "cancelled" || appointment.status === "completed"
                                          }
                                        >
                                          Cancel Appointment
                                        </Button>
                                        <Button variant="outline" className="flex-1 border-pink-300 text-pink-700">
                                          Send Reminder
                                        </Button>
                                      </div>
                                    </div>
                                  </DialogContent>
                                </Dialog>
                                {appointment.status !== "cancelled" && appointment.status !== "completed" && (
                                  <Button
                                    size="sm"
                                    variant="outline"
                                    onClick={() => handleCancelAppointment(appointment.id)}
                                    className="border-red-300 text-red-700 hover:bg-red-50"
                                  >
                                    Cancel
                                  </Button>
                                )}
                              </div>
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Availability Tab */}
            {selectedTab === "availability" && (
              <div className="space-y-6">
                {/* Business Hours */}
                <Card className="border-pink-200 bg-white/80 backdrop-blur-sm">
                  <CardHeader>
                    <CardTitle className="text-pink-800">Business Hours</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      {Object.entries(businessHours).map(([day, hours]) => (
                        <div key={day} className="flex items-center space-x-4 p-3 border border-pink-100 rounded-lg">
                          <div className="w-24">
                            <span className="font-medium text-pink-800 capitalize">{day}</span>
                          </div>
                          <div className="flex items-center space-x-2">
                            <input
                              type="checkbox"
                              checked={!hours.closed}
                              onChange={(e) =>
                                setBusinessHours({
                                  ...businessHours,
                                  [day]: { ...hours, closed: !e.target.checked },
                                })
                              }
                              className="rounded border-pink-300"
                            />
                            <Label className="text-sm text-pink-700">Open</Label>
                          </div>
                          {!hours.closed && (
                            <>
                              <div className="flex items-center space-x-2">
                                <Label className="text-sm text-pink-700">From:</Label>
                                <Input
                                  type="time"
                                  value={hours.open}
                                  onChange={(e) =>
                                    setBusinessHours({
                                      ...businessHours,
                                      [day]: { ...hours, open: e.target.value },
                                    })
                                  }
                                  className="w-32 border-pink-200"
                                />
                              </div>
                              <div className="flex items-center space-x-2">
                                <Label className="text-sm text-pink-700">To:</Label>
                                <Input
                                  type="time"
                                  value={hours.close}
                                  onChange={(e) =>
                                    setBusinessHours({
                                      ...businessHours,
                                      [day]: { ...hours, close: e.target.value },
                                    })
                                  }
                                  className="w-32 border-pink-200"
                                />
                              </div>
                            </>
                          )}
                          {hours.closed && <span className="text-gray-500 italic">Closed</span>}
                        </div>
                      ))}
                      <Button className="bg-gradient-to-r from-pink-500 to-amber-500 hover:from-pink-600 hover:to-amber-600">
                        Save Business Hours
                      </Button>
                    </div>
                  </CardContent>
                </Card>

                {/* Blocked Dates */}
                <Card className="border-pink-200 bg-white/80 backdrop-blur-sm">
                  <CardHeader className="flex flex-row items-center justify-between">
                    <CardTitle className="text-pink-800">Blocked Dates & Times</CardTitle>
                    <Dialog>
                      <DialogTrigger asChild>
                        <Button className="bg-gradient-to-r from-pink-500 to-amber-500 hover:from-pink-600 hover:to-amber-600">
                          Block Date/Time
                        </Button>
                      </DialogTrigger>
                      <DialogContent className="bg-white max-w-md">
                        <DialogHeader>
                          <DialogTitle className="text-pink-800">Block Date or Time</DialogTitle>
                        </DialogHeader>
                        <div className="space-y-4">
                          <div>
                            <Label htmlFor="blockDate" className="text-pink-800">
                              Date
                            </Label>
                            <Input
                              id="blockDate"
                              type="date"
                              value={newBlockedDate.date}
                              onChange={(e) => setNewBlockedDate({ ...newBlockedDate, date: e.target.value })}
                              className="border-pink-200"
                            />
                          </div>
                          <div>
                            <Label htmlFor="blockReason" className="text-pink-800">
                              Reason
                            </Label>
                            <Input
                              id="blockReason"
                              value={newBlockedDate.reason}
                              onChange={(e) => setNewBlockedDate({ ...newBlockedDate, reason: e.target.value })}
                              placeholder="e.g., Personal Day, Training, Holiday"
                              className="border-pink-200"
                            />
                          </div>
                          <div>
                            <Label className="text-pink-800">Block Type</Label>
                            <Select
                              value={newBlockedDate.type}
                              onValueChange={(value: "full-day" | "partial") => setNewBlockedDate({ ...newBlockedDate, type: value })}
                            >
                              <SelectTrigger className="border-pink-200">
                                <SelectValue />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="full-day">Full Day</SelectItem>
                                <SelectItem value="partial">Specific Times</SelectItem>
                              </SelectContent>
                            </Select>
                          </div>
                          {newBlockedDate.type === "partial" && (
                            <div>
                              <Label className="text-pink-800 mb-2 block">Select Time Slots to Block</Label>
                              <div className="grid grid-cols-2 gap-2 max-h-32 overflow-y-auto">
                                {[
                                  "9:00 AM",
                                  "10:00 AM",
                                  "11:00 AM",
                                  "12:00 PM",
                                  "1:00 PM",
                                  "2:00 PM",
                                  "3:00 PM",
                                  "4:00 PM",
                                  "5:00 PM",
                                  "6:00 PM",
                                ].map((time) => (
                                  <div key={time} className="flex items-center space-x-2">
                                    <input
                                      type="checkbox"
                                      id={`time-${time}`}
                                      checked={newBlockedDate.timeSlots?.includes(time) || false}
                                      onChange={(e) => {
                                        const currentTimeSlots = newBlockedDate.timeSlots || []
                                        if (e.target.checked) {
                                          setNewBlockedDate({
                                            ...newBlockedDate,
                                            timeSlots: [...currentTimeSlots, time],
                                          })
                                        } else {
                                          setNewBlockedDate({
                                            ...newBlockedDate,
                                            timeSlots: currentTimeSlots.filter((t) => t !== time),
                                          })
                                        }
                                      }}
                                      className="rounded border-pink-300"
                                    />
                                    <Label htmlFor={`time-${time}`} className="text-sm text-pink-700">
                                      {time}
                                    </Label>
                                  </div>
                                ))}
                              </div>
                            </div>
                          )}
                          <Button
                            onClick={addBlockedDate}
                            disabled={!newBlockedDate.date || !newBlockedDate.reason}
                            className="w-full bg-gradient-to-r from-pink-500 to-amber-500 hover:from-pink-600 hover:to-amber-600"
                          >
                            Block Date/Time
                          </Button>
                        </div>
                      </DialogContent>
                    </Dialog>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      {blockedDates.map((blocked, index) => (
                        <div
                          key={index}
                          className="flex items-center justify-between p-3 border border-pink-200 rounded-lg bg-pink-50"
                        >
                          <div>
                            <div className="font-semibold text-pink-800">
                              {new Date(blocked.date).toLocaleDateString()}
                            </div>
                            <div className="text-sm text-pink-600">{blocked.reason}</div>
                            {blocked.type === "partial" && (
                              <div className="text-xs text-gray-600 mt-1">
                                Blocked times: {blocked.timeSlots?.join(", ")}
                              </div>
                            )}
                            {blocked.type === "full-day" && (
                              <div className="text-xs text-gray-600 mt-1">Full day blocked</div>
                            )}
                          </div>
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => removeBlockedDate(index)}
                            className="border-red-300 text-red-700 hover:bg-red-50"
                          >
                            Remove
                          </Button>
                        </div>
                      ))}
                      {blockedDates.length === 0 && (
                        <div className="text-center py-8 text-gray-500">
                          No blocked dates or times. Click "Block Date/Time" to add restrictions.
                        </div>
                      )}
                    </div>
                  </CardContent>
                </Card>

                {/* Quick Actions */}
                <Card className="border-pink-200 bg-white/80 backdrop-blur-sm">
                  <CardHeader>
                    <CardTitle className="text-pink-800">Quick Actions</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="grid md:grid-cols-3 gap-4">
                      <Button
                        variant="outline"
                        className="h-20 flex flex-col border-pink-200 hover:bg-pink-50"
                        onClick={() => {
                          const today = new Date().toISOString().split("T")[0]
                          setNewBlockedDate({ ...newBlockedDate, date: today, reason: "Emergency Closure" })
                        }}
                      >
                        <Calendar className="h-6 w-6 text-pink-500 mb-1" />
                        <span className="text-sm">Block Today</span>
                      </Button>
                      <Button
                        variant="outline"
                        className="h-20 flex flex-col border-pink-200 hover:bg-pink-50"
                        onClick={() => {
                          const tomorrow = new Date()
                          tomorrow.setDate(tomorrow.getDate() + 1)
                          const tomorrowStr = tomorrow.toISOString().split("T")[0]
                          setNewBlockedDate({ ...newBlockedDate, date: tomorrowStr, reason: "Personal Day" })
                        }}
                      >
                        <Calendar className="h-6 w-6 text-pink-500 mb-1" />
                        <span className="text-sm">Block Tomorrow</span>
                      </Button>
                      <Button
                        variant="outline"
                        className="h-20 flex flex-col border-pink-200 hover:bg-pink-50"
                        onClick={() => {
                          // Block lunch time (12-1 PM) for the next 7 days
                          const lunchBlocks: BlockedDate[] = []
                          for (let i = 0; i < 7; i++) {
                            const date = new Date()
                            date.setDate(date.getDate() + i)
                            lunchBlocks.push({
                              date: date.toISOString().split("T")[0],
                              reason: "Lunch Break",
                              type: "partial" as const,
                              timeSlots: ["12:00 PM"],
                            })
                          }
                          setBlockedDates([...blockedDates, ...lunchBlocks])
                        }}
                      >
                        <Clock className="h-6 w-6 text-pink-500 mb-1" />
                        <span className="text-sm">Block Lunch Hours</span>
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              </div>
            )}

            {/* Service Combinations Tab */}
            {selectedTab === "service-combinations" && (
              <AdminServiceCombinations />
            )}

            {/* Services Tab */}
            {selectedTab === "services" && (
              <Card className="border-pink-200 bg-white/80 backdrop-blur-sm">
                <CardHeader className="flex flex-row items-center justify-between">
                  <CardTitle className="text-pink-800">Service Management</CardTitle>
                  <Dialog>
                    <DialogTrigger asChild>
                      <Button className="bg-gradient-to-r from-pink-500 to-amber-500 hover:from-pink-600 hover:to-amber-600">
                        Add New Service
                      </Button>
                    </DialogTrigger>
                    <DialogContent className="bg-white max-w-2xl max-h-[90vh] overflow-y-auto">
                      <DialogHeader>
                        <DialogTitle className="text-pink-800">
                          {editingService ? "Edit Service" : "Add New Service"}
                        </DialogTitle>
                      </DialogHeader>
                      <div className="space-y-4">
                        <div className="grid grid-cols-2 gap-4">
                          <div>
                            <Label htmlFor="serviceName" className="text-pink-800">
                              Service Name
                            </Label>
                            <Input
                              id="serviceName"
                              value={editingService ? editingService.name : newService.name}
                              onChange={(e) =>
                                editingService
                                  ? setEditingService({ ...editingService, name: e.target.value })
                                  : setNewService({ ...newService, name: e.target.value })
                              }
                              placeholder="e.g., Gel Manicure"
                              className="border-pink-200"
                            />
                          </div>
                          <div>
                            <Label htmlFor="serviceCategory" className="text-pink-800">
                              Category
                            </Label>
                            <Select
                              value={editingService ? editingService.category : newService.category}
                              onValueChange={(value) =>
                                editingService
                                  ? setEditingService({ ...editingService, category: value })
                                  : setNewService({ ...newService, category: value })
                              }
                            >
                              <SelectTrigger className="border-pink-200">
                                <SelectValue placeholder="Select category" />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="Classic">Classic</SelectItem>
                                <SelectItem value="Art">Nail Art</SelectItem>
                                <SelectItem value="Extensions">Extensions</SelectItem>
                                <SelectItem value="Luxury">Luxury</SelectItem>
                                <SelectItem value="Pedicure">Pedicure</SelectItem>
                              </SelectContent>
                            </Select>
                          </div>
                        </div>

                        <div className="grid grid-cols-2 gap-4">
                          <div>
                            <Label htmlFor="servicePrice" className="text-pink-800">
                              Price ($)
                            </Label>
                            <Input
                              id="servicePrice"
                              type="number"
                              value={editingService ? editingService.price : newService.price}
                              onChange={(e) =>
                                editingService
                                  ? setEditingService({ ...editingService, price: Number.parseInt(e.target.value) })
                                  : setNewService({ ...newService, price: e.target.value })
                              }
                              placeholder="45"
                              className="border-pink-200"
                            />
                          </div>
                          <div>
                            <Label htmlFor="serviceDuration" className="text-pink-800">
                              Duration (minutes)
                            </Label>
                            <Input
                              id="serviceDuration"
                              type="number"
                              value={editingService ? editingService.duration : newService.duration}
                              onChange={(e) =>
                                editingService
                                  ? setEditingService({ ...editingService, duration: Number.parseInt(e.target.value) })
                                  : setNewService({ ...newService, duration: e.target.value })
                              }
                              placeholder="60"
                              className="border-pink-200"
                            />
                          </div>
                        </div>

                        <div>
                          <Label htmlFor="serviceDescription" className="text-pink-800">
                            Description
                          </Label>
                          <Textarea
                            id="serviceDescription"
                            value={editingService ? editingService.description : newService.description}
                            onChange={(e) =>
                              editingService
                                ? setEditingService({ ...editingService, description: e.target.value })
                                : setNewService({ ...newService, description: e.target.value })
                            }
                            placeholder="Detailed description of the service..."
                            className="border-pink-200 min-h-[80px]"
                          />
                        </div>

                        <div>
                          <Label htmlFor="serviceImage" className="text-pink-800">
                            Service Image URL
                          </Label>
                          <Input
                            id="serviceImage"
                            value={editingService ? editingService.image : newService.image}
                            onChange={(e) =>
                              editingService
                                ? setEditingService({ ...editingService, image: e.target.value })
                                : setNewService({ ...newService, image: e.target.value })
                            }
                            placeholder="/placeholder.svg?height=200&width=300&text=Service+Name"
                            className="border-pink-200"
                          />
                        </div>

                        <div className="flex items-center space-x-2">
                          <input
                            type="checkbox"
                            id="serviceActive"
                            checked={editingService ? editingService.active : newService.active}
                            onChange={(e) =>
                              editingService
                                ? setEditingService({ ...editingService, active: e.target.checked })
                                : setNewService({ ...newService, active: e.target.checked })
                            }
                            className="rounded border-pink-300"
                          />
                          <Label htmlFor="serviceActive" className="text-pink-800">
                            Active (visible to customers)
                          </Label>
                        </div>

                        <div className="flex space-x-3">
                          <Button
                            onClick={() => {
                              if (editingService) {
                                // Update existing service
                                setServices(services.map((s) => (s.id === editingService.id ? editingService : s)))
                                setEditingService(null)
                              } else {
                                // Add new service
                                const id = newService.name.toLowerCase().replace(/\s+/g, "-")
                                setServices([
                                  ...services,
                                  {
                                    ...newService,
                                    id,
                                    price: Number.parseInt(newService.price),
                                    duration: Number.parseInt(newService.duration),
                                  },
                                ])
                                setNewService({
                                  name: "",
                                  price: "",
                                  duration: "",
                                  category: "",
                                  description: "",
                                  image: "",
                                  active: true,
                                })
                              }
                            }}
                            disabled={
                              !(
                                (editingService?.name || newService.name) &&
                                (editingService?.category || newService.category) &&
                                (editingService?.price || newService.price) &&
                                (editingService?.duration || newService.duration)
                              )
                            }
                            className="flex-1 bg-gradient-to-r from-pink-500 to-amber-500 hover:from-pink-600 hover:to-amber-600"
                          >
                            {editingService ? "Update Service" : "Add Service"}
                          </Button>
                          <Button
                            variant="outline"
                            onClick={() => {
                              setEditingService(null)
                              setNewService({
                                name: "",
                                price: "",
                                duration: "",
                                category: "",
                                description: "",
                                image: "",
                                active: true,
                              })
                            }}
                            className="border-pink-300 text-pink-700"
                          >
                            Cancel
                          </Button>
                        </div>
                      </div>
                    </DialogContent>
                  </Dialog>
                </CardHeader>
                <CardContent>
                  <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-4">
                    {services.map((service) => (
                      <Card key={service.id} className="border-pink-200 overflow-hidden">
                        <div className="relative">
                          <img
                            src={service.image || "/placeholder.svg"}
                            alt={service.name}
                            className="w-full h-32 object-cover"
                          />
                          <div className="absolute top-2 left-2 flex space-x-1">
                            <Badge className={`text-white text-xs ${service.active ? "bg-green-500" : "bg-gray-500"}`}>
                              {service.active ? "Active" : "Inactive"}
                            </Badge>
                            <Badge className="bg-pink-500 text-white text-xs">{service.category}</Badge>
                          </div>
                        </div>
                        <CardContent className="p-4">
                          <h3 className="font-semibold text-pink-800 mb-1">{service.name}</h3>
                          <p className="text-sm text-gray-600 mb-2 line-clamp-2">{service.description}</p>
                          <div className="flex justify-between items-center mb-3">
                            <span className="text-lg font-bold text-amber-600">${service.price}</span>
                            <span className="text-sm text-gray-500 flex items-center">
                              <Clock className="h-3 w-3 mr-1" />
                              {service.duration}m
                            </span>
                          </div>
                          <div className="flex space-x-2">
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => setEditingService(service)}
                              className="flex-1 text-xs border-pink-300 text-pink-700"
                            >
                              Edit
                            </Button>
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() =>
                                setServices(
                                  services.map((s) => (s.id === service.id ? { ...s, active: !s.active } : s)),
                                )
                              }
                              className={`flex-1 text-xs ${service.active ? "border-red-300 text-red-700" : "border-green-300 text-green-700"}`}
                            >
                              {service.active ? "Deactivate" : "Activate"}
                            </Button>
                          </div>
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Users Tab */}
            {selectedTab === "users" && (
              <Card className="border-pink-200 bg-white/80 backdrop-blur-sm">
                <CardHeader>
                  <CardTitle className="text-pink-800">Client Management</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="overflow-x-auto">
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead className="text-pink-700">Name</TableHead>
                          <TableHead className="text-pink-700">Email</TableHead>
                          <TableHead className="text-pink-700">Member</TableHead>
                          <TableHead className="text-pink-700">Total Spent</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {users.map((user) => (
                          <TableRow key={user.id}>
                            <TableCell className="text-pink-800 font-medium">{user.name}</TableCell>
                            <TableCell className="text-pink-700">{user.email}</TableCell>
                            <TableCell>
                              <Badge
                                variant={user.member ? "default" : "secondary"}
                                className={user.member ? "bg-gradient-to-r from-pink-500 to-amber-500" : ""}
                              >
                                {user.member ? "Member" : "Guest"}
                              </Badge>
                            </TableCell>
                            <TableCell className="text-pink-800 font-semibold">${user.totalSpent}</TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Portfolio Tab */}
            {selectedTab === "portfolio" && (
              <Card className="border-pink-200 bg-white/80 backdrop-blur-sm">
                <CardHeader className="flex flex-row items-center justify-between">
                  <CardTitle className="text-pink-800">Portfolio Management</CardTitle>
                  <Dialog>
                    <DialogTrigger asChild>
                      <Button className="bg-gradient-to-r from-pink-500 to-amber-500 hover:from-pink-600 hover:to-amber-600">
                        <Camera className="h-4 w-4 mr-2" />
                        Add Photo
                      </Button>
                    </DialogTrigger>
                    <DialogContent className="bg-white max-w-md max-h-[90vh] overflow-y-auto">
                      <DialogHeader>
                        <DialogTitle className="text-pink-800">Add Client Photo</DialogTitle>
                      </DialogHeader>
                      <div className="space-y-4">
                        <div>
                          <Label htmlFor="photoUpload" className="text-pink-800">
                            Upload Photo
                          </Label>
                          <div className="mt-2">
                            <label className="flex flex-col items-center justify-center w-full h-32 border-2 border-pink-300 border-dashed rounded-lg cursor-pointer bg-pink-50 hover:bg-pink-100">
                              <div className="flex flex-col items-center justify-center pt-5 pb-6">
                                <Upload className="w-8 h-8 mb-2 text-pink-500" />
                                <p className="text-sm text-pink-600">
                                  {uploadedFile ? uploadedFile.name : "Click to upload photo"}
                                </p>
                              </div>
                              <input
                                id="photoUpload"
                                type="file"
                                accept="image/*"
                                className="hidden"
                                onChange={handlePhotoUpload}
                              />
                            </label>
                          </div>
                        </div>
                        <div>
                          <Label htmlFor="clientSelect" className="text-pink-800">
                            Select Client
                          </Label>
                          <Select
                            value={newPhoto.clientId}
                            onValueChange={(value) => setNewPhoto({ ...newPhoto, clientId: value })}
                          >
                            <SelectTrigger className="border-pink-200">
                              <SelectValue placeholder="Choose client" />
                            </SelectTrigger>
                            <SelectContent>
                              {users.map((user) => (
                                <SelectItem key={user.id} value={user.id.toString()}>
                                  {user.name}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        </div>
                        <div>
                          <Label className="text-pink-800 mb-3 block">Services Performed</Label>
                          <div className="max-h-48 overflow-y-auto border border-pink-200 rounded-lg p-3 space-y-2">
                            {services
                              .filter((service) => service.active)
                              .map((service) => (
                                <div key={service.id} className="flex items-center space-x-2">
                                  <input
                                    type="checkbox"
                                    id={`service-${service.id}`}
                                    checked={newPhoto.services.includes(service.id)}
                                    onChange={(e) => {
                                      if (e.target.checked) {
                                        setNewPhoto({ ...newPhoto, services: [...newPhoto.services, service.id] })
                                      } else {
                                        setNewPhoto({
                                          ...newPhoto,
                                          services: newPhoto.services.filter((id) => id !== service.id),
                                        })
                                      }
                                    }}
                                    className="rounded border-pink-300"
                                  />
                                  <Label htmlFor={`service-${service.id}`} className="text-sm text-pink-700 flex-1">
                                    {service.name} - ${service.price}
                                  </Label>
                                </div>
                              ))}
                          </div>
                          <p className="text-xs text-gray-500 mt-2">Select all services performed for this photo</p>
                        </div>
                        <div className="flex items-center space-x-2">
                          <input
                            type="checkbox"
                            id="featured"
                            checked={newPhoto.featured}
                            onChange={(e) => setNewPhoto({ ...newPhoto, featured: e.target.checked })}
                            className="rounded border-pink-300"
                          />
                          <Label htmlFor="featured" className="text-pink-800">
                            Feature on homepage
                          </Label>
                        </div>
                        <Button
                          onClick={handleAddPhoto}
                          disabled={!uploadedFile || !newPhoto.clientId || newPhoto.services.length === 0}
                          className="w-full bg-gradient-to-r from-pink-500 to-amber-500 hover:from-pink-600 hover:to-amber-600"
                        >
                          Add to Portfolio
                        </Button>
                      </div>
                    </DialogContent>
                  </Dialog>
                </CardHeader>
                <CardContent>
                  <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-4">
                    {portfolioPhotos.map((photo) => (
                      <Card key={photo.id} className="border-pink-200 overflow-hidden">
                        <div className="relative">
                          <img
                            src={photo.url || "/placeholder.svg"}
                            alt={photo.service}
                            className="w-full h-48 object-cover"
                          />
                          <div className="absolute top-2 left-2 flex space-x-1">
                            {photo.featured && (
                              <Badge className="bg-amber-500 text-white text-xs">
                                <Star className="h-3 w-3 mr-1" />
                                Featured
                              </Badge>
                            )}
                            {photo.approved ? (
                              <Badge className="bg-green-500 text-white text-xs">Approved</Badge>
                            ) : (
                              <Badge className="bg-gray-500 text-white text-xs">Pending</Badge>
                            )}
                          </div>
                        </div>
                        <CardContent className="p-4">
                          <h3 className="font-semibold text-pink-800 mb-1">{photo.service}</h3>
                          <p className="text-sm text-gray-600 mb-2">Client: {photo.clientName}</p>
                          <p className="text-sm text-gray-500 mb-3">{photo.date}</p>
                          <div className="flex space-x-2">
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => toggleFeatured(photo.id)}
                              className={`flex-1 text-xs ${
                                photo.featured ? "border-amber-300 text-amber-700" : "border-pink-300 text-pink-700"
                              }`}
                            >
                              {photo.featured ? "Unfeature" : "Feature"}
                            </Button>
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => toggleApproved(photo.id)}
                              className={`flex-1 text-xs ${
                                photo.approved ? "border-green-300 text-green-700" : "border-gray-300 text-gray-700"
                              }`}
                            >
                              {photo.approved ? "Approved" : "Approve"}
                            </Button>
                          </div>
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Referrals Tab */}
            {selectedTab === "referrals" && (
              <Card className="border-pink-200 bg-white/80 backdrop-blur-sm">
                <CardHeader>
                  <CardTitle className="text-pink-800">Referral Program Analytics</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid md:grid-cols-4 gap-4 mb-6">
                    <div className="text-center">
                      <div className="text-2xl font-bold text-pink-600">24</div>
                      <div className="text-sm text-gray-600">Total Referrals</div>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold text-green-600">18</div>
                      <div className="text-sm text-gray-600">Successful</div>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold text-amber-600">$180</div>
                      <div className="text-sm text-gray-600">Credits Paid</div>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold text-purple-600">$1,350</div>
                      <div className="text-sm text-gray-600">Revenue Generated</div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Content Management Tabs */}
            {selectedTab === "homepage-stats" && (
              <AdminHomepageStats />
            )}

            {selectedTab === "customer-reviews" && (
              <AdminCustomerReviews />
            )}

            {selectedTab === "business-info" && (
              <AdminBusinessInfo />
            )}

            {selectedTab === "owner-info" && (
              <div className="space-y-6">
                <div>
                  <h2 className="text-2xl font-bold text-gray-900">Owner Profile</h2>
                  <p className="text-gray-600">Manage owner information and bio</p>
                </div>
                <Card className="border-pink-200">
                  <CardContent className="p-6 text-center">
                    <User className="h-12 w-12 text-pink-300 mx-auto mb-4" />
                    <h3 className="text-lg font-semibold text-pink-800 mb-2">Coming Soon</h3>
                    <p className="text-pink-600">Owner profile management will be available soon!</p>
                  </CardContent>
                </Card>
              </div>
            )}

            {selectedTab === "booking-policies" && (
              <div className="space-y-6">
                <div>
                  <h2 className="text-2xl font-bold text-gray-900">Booking Policies</h2>
                  <p className="text-gray-600">Manage terms, conditions, and policies</p>
                </div>
                <Card className="border-pink-200">
                  <CardContent className="p-6 text-center">
                    <FileText className="h-12 w-12 text-pink-300 mx-auto mb-4" />
                    <h3 className="text-lg font-semibold text-pink-800 mb-2">Coming Soon</h3>
                    <p className="text-pink-600">Booking policies management will be available soon!</p>
                  </CardContent>
                </Card>
              </div>
            )}

            {/* Announcements Tab */}
            {selectedTab === "announcements" && (
              <AdminAnnouncements />
            )}
          </div>
        </SidebarInset>
      </div>
    </AdminSidebarProvider>
  )
}


