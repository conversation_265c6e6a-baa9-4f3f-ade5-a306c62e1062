"use client"

import { <PERSON><PERSON><PERSON>t, Bell, <PERSON>u, <PERSON><PERSON><PERSON><PERSON>, User } from "lucide-react"
import { Button } from "@/components/ui/button"
import { GoogleAuth } from "@/components/auth/google-auth"
import { useAuth } from "@/components/auth/auth-provider"
import { useState } from "react"
import Link from "next/link"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"

interface AppHeaderProps {
  title: string
  showBack?: boolean
  showNotifications?: boolean
  showMenu?: boolean
}

export function AppHeader({ title, showBack, showNotifications, showMenu }: AppHeaderProps) {
  const { user, isSignedIn, signOut } = useAuth()
  const [authOpen, setAuthOpen] = useState(false)
  const [authMode, setAuthMode] = useState<"signin" | "signup">("signin")

  const handleAuthSuccess = () => {
    // Auth provider handles the sign in automatically
    setAuthOpen(false)
  }

  return (
    <>
      <div className="sticky top-0 bg-white/95 backdrop-blur-md border-b border-pink-200 z-40 safe-area-top">
        <div className="flex items-center justify-between px-4 py-3">
          <div className="flex items-center">
            {showBack && (
              <Button variant="ghost" size="sm" className="mr-2 p-2" asChild>
                <Link href="/">
                  <ArrowLeft className="h-5 w-5 text-pink-700" />
                </Link>
              </Button>
            )}
            {showMenu && (
              <Button variant="ghost" size="sm" className="mr-2 p-2">
                <Menu className="h-5 w-5 text-pink-700" />
              </Button>
            )}
          </div>

          <h1
            className="text-lg font-bold bg-gradient-to-r from-pink-600 to-amber-500 bg-clip-text text-transparent text-center flex-1"
            style={{ fontFamily: "Dancing Script, cursive" }}
          >
            {title}
          </h1>

          <div className="flex items-center space-x-2">
            {showNotifications && (
              <Button variant="ghost" size="sm" className="p-2">
                <Bell className="h-5 w-5 text-pink-700" />
              </Button>
            )}

            {isSignedIn ? (
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" size="sm" className="p-2">
                    <img
                      src={user?.avatar_url || "/placeholder.svg?height=32&width=32&text=U"}
                      alt={user?.first_name || "User"}
                      className="h-6 w-6 rounded-full border border-pink-200"
                    />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end" className="w-48">
                  <DropdownMenuItem asChild>
                    <Link href="/profile" className="flex items-center">
                      <User className="h-4 w-4 mr-2" />
                      Profile
                    </Link>
                  </DropdownMenuItem>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem onClick={signOut} className="text-red-600">
                    <LogOut className="h-4 w-4 mr-2" />
                    Sign Out
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            ) : (
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setAuthOpen(true)}
                className="text-pink-700 hover:bg-pink-50"
              >
                Sign In
              </Button>
            )}
          </div>
        </div>
      </div>

      <GoogleAuth
        isOpen={authOpen}
        onClose={() => setAuthOpen(false)}
        mode={authMode}
        onModeChange={setAuthMode}
        onSuccess={handleAuthSuccess}
      />
    </>
  )
}
