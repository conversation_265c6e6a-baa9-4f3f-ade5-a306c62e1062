"use client"

import { useState, useRef } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Checkbox } from '@/components/ui/checkbox'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Upload, Camera, X, Image as ImageIcon } from 'lucide-react'
import { useAuth } from '@/components/auth/auth-provider'
import { supabase } from '@/lib/supabase'
import { toast } from 'sonner'

interface ImageUploadProps {
  onUploadComplete?: (imageUrl: string) => void
}

interface Appointment {
  id: string
  appointment_date: string
  services: { name: string }[]
}

export function ImageUpload({ onUploadComplete }: ImageUploadProps) {
  const [isOpen, setIsOpen] = useState(false)
  const [isUploading, setIsUploading] = useState(false)
  const [selectedFile, setSelectedFile] = useState<File | null>(null)
  const [previewUrl, setPreviewUrl] = useState<string | null>(null)
  const [selectedAppointment, setSelectedAppointment] = useState<string>('')
  const [isPrivate, setIsPrivate] = useState(false)
  const [appointments, setAppointments] = useState<Appointment[]>([])
  const [loadingAppointments, setLoadingAppointments] = useState(false)
  const fileInputRef = useRef<HTMLInputElement>(null)
  const { user } = useAuth()

  const fetchUserAppointments = async () => {
    if (!user) return

    setLoadingAppointments(true)
    try {
      const { data, error } = await supabase
        .from('appointments')
        .select(`
          id,
          appointment_date,
          services:service_ids (
            name
          )
        `)
        .eq('user_id', user.id)
        .eq('status', 'completed')
        .order('appointment_date', { ascending: false })

      if (error) {
        console.error('Error fetching appointments:', error)
        toast.error('Failed to load appointments')
        return
      }

      setAppointments(data || [])
    } catch (error) {
      console.error('Error fetching appointments:', error)
      toast.error('Failed to load appointments')
    } finally {
      setLoadingAppointments(false)
    }
  }

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (!file) return

    // Validate file type
    if (!file.type.startsWith('image/')) {
      toast.error('Please select an image file')
      return
    }

    // Validate file size (max 5MB)
    if (file.size > 5 * 1024 * 1024) {
      toast.error('Image must be less than 5MB')
      return
    }

    setSelectedFile(file)
    
    // Create preview URL
    const url = URL.createObjectURL(file)
    setPreviewUrl(url)
  }

  const handleUpload = async () => {
    if (!selectedFile || !selectedAppointment || !user) {
      toast.error('Please select an image and appointment')
      return
    }

    setIsUploading(true)
    try {
      // Upload image to Supabase Storage
      const fileExt = selectedFile.name.split('.').pop()
      const fileName = `${user.id}/${Date.now()}.${fileExt}`
      
      const { data: uploadData, error: uploadError } = await supabase.storage
        .from('nail-images')
        .upload(fileName, selectedFile)

      if (uploadError) {
        console.error('Upload error:', uploadError)
        toast.error('Failed to upload image')
        return
      }

      // Get public URL
      const { data: { publicUrl } } = supabase.storage
        .from('nail-images')
        .getPublicUrl(fileName)

      // Save image record to database
      const { error: dbError } = await supabase
        .from('nail_images')
        .insert({
          user_id: user.id,
          appointment_id: selectedAppointment,
          image_url: publicUrl,
          is_private: isPrivate,
          is_featured: false,
          likes_count: 0
        })

      if (dbError) {
        console.error('Database error:', dbError)
        toast.error('Failed to save image record')
        return
      }

      toast.success('Image uploaded successfully!')
      onUploadComplete?.(publicUrl)
      handleClose()
    } catch (error) {
      console.error('Upload error:', error)
      toast.error('Failed to upload image')
    } finally {
      setIsUploading(false)
    }
  }

  const handleClose = () => {
    setIsOpen(false)
    setSelectedFile(null)
    setPreviewUrl(null)
    setSelectedAppointment('')
    setIsPrivate(false)
    if (fileInputRef.current) {
      fileInputRef.current.value = ''
    }
  }

  const handleOpen = () => {
    setIsOpen(true)
    fetchUserAppointments()
  }

  if (!user) {
    return (
      <Card className="border-pink-200 bg-white/80 backdrop-blur-sm">
        <CardContent className="p-6 text-center">
          <ImageIcon className="h-12 w-12 text-pink-300 mx-auto mb-4" />
          <h3 className="text-lg font-semibold text-pink-800 mb-2">Share Your Nail Art</h3>
          <p className="text-pink-600 mb-4">Sign in to upload and share photos of your beautiful nails!</p>
          <Button className="bg-gradient-to-r from-pink-500 to-amber-500 hover:from-pink-600 hover:to-amber-600">
            Sign In to Upload
          </Button>
        </CardContent>
      </Card>
    )
  }

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <Button 
          onClick={handleOpen}
          className="bg-gradient-to-r from-pink-500 to-amber-500 hover:from-pink-600 hover:to-amber-600"
        >
          <Camera className="h-4 w-4 mr-2" />
          Upload Photo
        </Button>
      </DialogTrigger>
      
      <DialogContent className="bg-white max-w-md max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="text-pink-800">Upload Nail Photo</DialogTitle>
        </DialogHeader>

        <div className="space-y-4">
          {/* File Upload */}
          <div>
            <Label className="text-pink-800 mb-2 block">Select Image</Label>
            <div className="border-2 border-dashed border-pink-200 rounded-lg p-6 text-center">
              {previewUrl ? (
                <div className="relative">
                  <img 
                    src={previewUrl} 
                    alt="Preview" 
                    className="max-w-full h-48 object-cover rounded-lg mx-auto"
                  />
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={() => {
                      setSelectedFile(null)
                      setPreviewUrl(null)
                      if (fileInputRef.current) fileInputRef.current.value = ''
                    }}
                    className="absolute top-2 right-2 bg-white"
                  >
                    <X className="h-4 w-4" />
                  </Button>
                </div>
              ) : (
                <div>
                  <Upload className="h-12 w-12 text-pink-300 mx-auto mb-4" />
                  <p className="text-pink-600 mb-2">Click to upload or drag and drop</p>
                  <p className="text-sm text-gray-500">PNG, JPG up to 5MB</p>
                </div>
              )}
              <Input
                ref={fileInputRef}
                type="file"
                accept="image/*"
                onChange={handleFileSelect}
                className="hidden"
              />
              <Button
                type="button"
                variant="outline"
                onClick={() => fileInputRef.current?.click()}
                className="mt-4 border-pink-300 text-pink-700"
              >
                Choose File
              </Button>
            </div>
          </div>

          {/* Appointment Selection */}
          <div>
            <Label className="text-pink-800 mb-2 block">Select Appointment</Label>
            <Select value={selectedAppointment} onValueChange={setSelectedAppointment}>
              <SelectTrigger className="border-pink-200">
                <SelectValue placeholder="Choose an appointment" />
              </SelectTrigger>
              <SelectContent>
                {loadingAppointments ? (
                  <SelectItem value="loading" disabled>Loading appointments...</SelectItem>
                ) : appointments.length === 0 ? (
                  <SelectItem value="none" disabled>No completed appointments found</SelectItem>
                ) : (
                  appointments.map((appointment) => (
                    <SelectItem key={appointment.id} value={appointment.id}>
                      {new Date(appointment.appointment_date).toLocaleDateString()} - {appointment.services?.map(s => s.name).join(', ')}
                    </SelectItem>
                  ))
                )}
              </SelectContent>
            </Select>
          </div>

          {/* Privacy Setting */}
          <div className="flex items-center space-x-2">
            <Checkbox
              id="isPrivate"
              checked={isPrivate}
              onCheckedChange={(checked) => setIsPrivate(checked as boolean)}
            />
            <Label htmlFor="isPrivate" className="text-sm text-pink-700">
              Make my images private (only visible to me)
            </Label>
          </div>

          {/* Upload Button */}
          <div className="flex space-x-3">
            <Button
              type="button"
              variant="outline"
              onClick={handleClose}
              className="flex-1 border-pink-300 text-pink-700"
            >
              Cancel
            </Button>
            <Button
              onClick={handleUpload}
              disabled={isUploading || !selectedFile || !selectedAppointment}
              className="flex-1 bg-gradient-to-r from-pink-500 to-amber-500 hover:from-pink-600 hover:to-amber-600"
            >
              {isUploading ? (
                <div className="flex items-center">
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  Uploading...
                </div>
              ) : (
                'Upload Photo'
              )}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}
