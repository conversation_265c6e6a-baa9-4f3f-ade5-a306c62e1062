import { NextRequest, NextResponse } from 'next/server'
import { createServerClient } from '@/lib/supabase'

export async function POST(request: NextRequest) {
  try {
    const supabase = createServerClient()

    // Check if user is admin (you might want to add additional security here)
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Execute database setup
    const setupQueries = [
      // Create users table
      `
      CREATE TABLE IF NOT EXISTS public.users (
        id UUID REFERENCES auth.users(id) ON DELETE CASCADE PRIMARY KEY,
        email TEXT UNIQUE NOT NULL,
        first_name TEXT NOT NULL,
        last_name TEXT NOT NULL,
        avatar_url TEXT,
        phone TEXT,
        is_admin BOOLEAN DEFAULT FALSE,
        is_member BOOLEAN DEFAULT FALSE,
        subscribe_announcements B<PERSON><PERSON><PERSON><PERSON> DEFAULT FALSE,
        email_verified BOOLEAN DEFAULT FALSE,
        referral_code TEXT UNIQUE,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      );
      `,
      
      // Create services table
      `
      CREATE TABLE IF NOT EXISTS public.services (
        id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
        name TEXT NOT NULL,
        description TEXT,
        price DECIMAL(10,2) NOT NULL,
        duration INTEGER NOT NULL,
        category TEXT NOT NULL,
        image_url TEXT,
        active BOOLEAN DEFAULT TRUE,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      );
      `,
      
      // Create appointments table
      `
      CREATE TABLE IF NOT EXISTS public.appointments (
        id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
        user_id UUID REFERENCES public.users(id) ON DELETE CASCADE NOT NULL,
        service_ids UUID[] NOT NULL,
        appointment_date DATE NOT NULL,
        appointment_time TIME NOT NULL,
        status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'confirmed', 'completed', 'cancelled', 'no_show')),
        total_price DECIMAL(10,2) NOT NULL,
        total_duration INTEGER NOT NULL,
        notes TEXT,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      );
      `,
      
      // Create nail_images table
      `
      CREATE TABLE IF NOT EXISTS public.nail_images (
        id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
        user_id UUID REFERENCES public.users(id) ON DELETE CASCADE NOT NULL,
        appointment_id UUID REFERENCES public.appointments(id) ON DELETE CASCADE NOT NULL,
        image_url TEXT NOT NULL,
        is_private BOOLEAN DEFAULT FALSE,
        is_featured BOOLEAN DEFAULT FALSE,
        likes_count INTEGER DEFAULT 0,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      );
      `,
      
      // Create image_likes table
      `
      CREATE TABLE IF NOT EXISTS public.image_likes (
        id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
        user_id UUID REFERENCES public.users(id) ON DELETE CASCADE NOT NULL,
        image_id UUID REFERENCES public.nail_images(id) ON DELETE CASCADE NOT NULL,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        UNIQUE(user_id, image_id)
      );
      `,
      
      // Create announcements table
      `
      CREATE TABLE IF NOT EXISTS public.announcements (
        id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
        title TEXT NOT NULL,
        content TEXT NOT NULL,
        sent_at TIMESTAMP WITH TIME ZONE,
        created_by UUID REFERENCES public.users(id) ON DELETE CASCADE NOT NULL,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      );
      `,
      
      // Create business_hours table
      `
      CREATE TABLE IF NOT EXISTS public.business_hours (
        id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
        day_of_week INTEGER NOT NULL CHECK (day_of_week >= 0 AND day_of_week <= 6),
        open_time TIME,
        close_time TIME,
        is_closed BOOLEAN DEFAULT FALSE,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        UNIQUE(day_of_week)
      );
      `,
      
      // Create blocked_dates table
      `
      CREATE TABLE IF NOT EXISTS public.blocked_dates (
        id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
        date DATE NOT NULL,
        reason TEXT NOT NULL,
        type TEXT DEFAULT 'full_day' CHECK (type IN ('full_day', 'partial')),
        blocked_times TEXT[],
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      );
      `
    ]

    // Execute all setup queries
    for (const query of setupQueries) {
      const { error } = await supabase.rpc('exec_sql', { sql: query })
      if (error) {
        console.error('Database setup error:', error)
        return NextResponse.json({ error: 'Database setup failed', details: error }, { status: 500 })
      }
    }

    // Insert default data
    const { error: seedError } = await supabase.rpc('exec_sql', {
      sql: `
        INSERT INTO public.services (name, description, price, duration, category, active) VALUES
        ('Classic Manicure', 'Traditional nail care with cuticle work, shaping, and polish application', 35.00, 45, 'Classic', true),
        ('Gel Manicure', 'Long-lasting gel polish application with base and top coat', 45.00, 60, 'Classic', true),
        ('Luxury Spa Manicure', 'Premium spa experience with exfoliation, massage, and luxury treatments', 65.00, 75, 'Classic', true),
        ('Simple Nail Art', 'Basic nail art designs including stripes, dots, and simple patterns', 55.00, 60, 'Art', true),
        ('Intricate Nail Art', 'Complex artistic designs with detailed patterns and multiple colors', 75.00, 90, 'Art', true),
        ('Gel Extensions', 'Gel nail extensions for length and strength', 70.00, 90, 'Extensions', true),
        ('Diamond Manicure', 'Ultimate luxury experience with premium products and diamond dust finish', 120.00, 90, 'Luxury', true),
        ('Classic Pedicure', 'Complete foot care with soak, exfoliation, and polish', 40.00, 50, 'Pedicure', true),
        ('Spa Pedicure', 'Luxury pedicure with extended massage and premium treatments', 60.00, 70, 'Pedicure', true)
        ON CONFLICT DO NOTHING;

        INSERT INTO public.business_hours (day_of_week, open_time, close_time, is_closed) VALUES
        (0, '10:00', '17:00', false),
        (1, '09:00', '19:00', false),
        (2, '09:00', '19:00', false),
        (3, '09:00', '19:00', false),
        (4, '09:00', '19:00', false),
        (5, '09:00', '19:00', false),
        (6, '09:00', '19:00', false)
        ON CONFLICT DO NOTHING;
      `
    })

    if (seedError) {
      console.error('Data seeding error:', seedError)
      return NextResponse.json({ error: 'Data seeding failed', details: seedError }, { status: 500 })
    }

    return NextResponse.json({ success: true, message: 'Database setup completed successfully' })

  } catch (error) {
    console.error('Setup error:', error)
    return NextResponse.json({ error: 'Setup failed', details: error }, { status: 500 })
  }
}
