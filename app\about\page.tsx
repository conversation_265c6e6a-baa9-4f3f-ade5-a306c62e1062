import { Card, CardContent } from "@/components/ui/card"
import { Heart, Award, Users, Clock } from "lucide-react"

export default function AboutPage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-rose-50 to-pink-100">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        <div className="text-center mb-16">
          <h1
            className="text-5xl md:text-6xl font-bold mb-6 bg-gradient-to-r from-pink-600 to-amber-500 bg-clip-text text-transparent"
            style={{ fontFamily: "Dancing Script, cursive" }}
          >
            About Nails by Lingg
          </h1>
          <p className="text-xl text-pink-800 max-w-3xl mx-auto">
            Where passion meets perfection in the art of nail care
          </p>
        </div>

        <div className="grid lg:grid-cols-2 gap-12 items-center mb-16">
          <div>
            <img
              src="/placeholder.svg?height=500&width=600"
              alt="Nails by Lingg Interior"
              className="rounded-lg shadow-lg"
            />
          </div>
          <div>
            <h2 className="text-3xl font-bold text-pink-800 mb-6" style={{ fontFamily: "Dancing Script, cursive" }}>
              Our Story
            </h2>
            <p className="text-pink-700 mb-4 leading-relaxed">
              Founded with a vision to elevate nail care to an art form, Nails by Lingg has been serving discerning
              clients for over 8 years. Our journey began with a simple belief: every client deserves to feel pampered,
              beautiful, and confident.
            </p>
            <p className="text-pink-700 mb-4 leading-relaxed">
              We combine traditional techniques with the latest innovations in nail care, using only premium products
              and maintaining the highest standards of hygiene and safety.
            </p>
            <p className="text-pink-700 leading-relaxed">
              Our team of certified nail artists brings creativity, skill, and passion to every appointment, ensuring
              you leave feeling absolutely radiant.
            </p>
          </div>
        </div>

        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8 mb-16">
          <Card className="border-pink-200 bg-white/80 backdrop-blur-sm text-center">
            <CardContent className="p-6">
              <Heart className="h-12 w-12 text-amber-500 mx-auto mb-4" />
              <h3 className="text-2xl font-bold text-pink-800 mb-2">8+</h3>
              <p className="text-pink-600">Years of Excellence</p>
            </CardContent>
          </Card>
          <Card className="border-pink-200 bg-white/80 backdrop-blur-sm text-center">
            <CardContent className="p-6">
              <Users className="h-12 w-12 text-amber-500 mx-auto mb-4" />
              <h3 className="text-2xl font-bold text-pink-800 mb-2">5000+</h3>
              <p className="text-pink-600">Happy Clients</p>
            </CardContent>
          </Card>
          <Card className="border-pink-200 bg-white/80 backdrop-blur-sm text-center">
            <CardContent className="p-6">
              <Award className="h-12 w-12 text-amber-500 mx-auto mb-4" />
              <h3 className="text-2xl font-bold text-pink-800 mb-2">15+</h3>
              <p className="text-pink-600">Awards Won</p>
            </CardContent>
          </Card>
          <Card className="border-pink-200 bg-white/80 backdrop-blur-sm text-center">
            <CardContent className="p-6">
              <Clock className="h-12 w-12 text-amber-500 mx-auto mb-4" />
              <h3 className="text-2xl font-bold text-pink-800 mb-2">24/7</h3>
              <p className="text-pink-600">Online Booking</p>
            </CardContent>
          </Card>
        </div>

        <div className="bg-white/80 backdrop-blur-sm rounded-lg p-8 border border-pink-200">
          <h2
            className="text-3xl font-bold text-center text-pink-800 mb-8"
            style={{ fontFamily: "Dancing Script, cursive" }}
          >
            Meet Our Master Nail Artist
          </h2>
          <div className="grid lg:grid-cols-2 gap-8 items-center">
            <div className="text-center lg:text-left">
              <img
                src="/placeholder.svg?height=300&width=300"
                alt="Master Nail Artist"
                className="w-64 h-64 rounded-full mx-auto lg:mx-0 mb-6 object-cover border-4 border-amber-200"
              />
            </div>
            <div>
              <h3 className="text-2xl font-bold text-pink-800 mb-4" style={{ fontFamily: "Dancing Script, cursive" }}>
                Sarah Michelle
              </h3>
              <p className="text-pink-700 mb-4 leading-relaxed">
                With over 12 years of experience in nail artistry, Sarah is a certified master nail technician and the
                creative force behind Nails by Lingg. She specializes in intricate nail art, gel extensions, and
                luxury manicures.
              </p>
              <p className="text-pink-700 mb-4 leading-relaxed">
                Sarah's work has been featured in top beauty magazines and she continues to train with international
                nail artists to bring the latest techniques to our studio.
              </p>
              <div className="flex flex-wrap gap-2">
                <span className="bg-pink-100 text-pink-800 px-3 py-1 rounded-full text-sm">
                  Certified Master Technician
                </span>
                <span className="bg-amber-100 text-amber-800 px-3 py-1 rounded-full text-sm">Nail Art Specialist</span>
                <span className="bg-pink-100 text-pink-800 px-3 py-1 rounded-full text-sm">Gel Extension Expert</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
