"use client"

import Link from "next/link"
import { usePathname } from "next/navigation"
import { Home, Calendar, User, Settings, Sparkles } from "lucide-react"
import { useAuth } from "@/components/auth/auth-provider"

const baseNavItems = [
  { href: "/", icon: Home, label: "Home" },
  { href: "/services", icon: Sparkles, label: "Services" },
  { href: "/booking", icon: Calendar, label: "Book" },
  { href: "/profile", icon: User, label: "Profile" },
]

const adminNavItem = { href: "/admin", icon: Settings, label: "Admin" }

export function BottomNav() {
  const pathname = usePathname()
  const { user } = useAuth()

  // Build navigation items based on user permissions
  const navItems = user?.is_admin
    ? [...baseNavItems, adminNavItem]
    : baseNavItems

  // Hide bottom nav on admin pages
  if (pathname.startsWith("/admin")) {
    return null
  }

  return (
    <div className="fixed bottom-0 left-0 right-0 bg-white border-t border-pink-200 safe-area-bottom z-50">
      <div className="flex justify-around items-center py-2 px-4">
        {navItems.map((item) => {
          const isActive = pathname === item.href
          const Icon = item.icon
          return (
            <Link
              key={item.href}
              href={item.href}
              className={`flex flex-col items-center py-2 px-3 rounded-lg transition-all ${
                isActive
                  ? "text-white bg-gradient-to-r from-pink-500 to-amber-500"
                  : "text-pink-600 hover:text-pink-800 hover:bg-pink-50"
              }`}
            >
              <Icon className="h-5 w-5 mb-1" />
              <span className="text-xs font-medium">{item.label}</span>
            </Link>
          )
        })}
      </div>
    </div>
  )
}
