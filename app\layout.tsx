import type React from "react"
import type { Metada<PERSON> } from "next"
import { Inter } from "next/font/google"
import "./globals.css"
import { BottomNav } from "@/components/ui/bottom-nav"
import { AuthProvider } from "@/components/auth/auth-provider"
import { Toaster } from "sonner"

const inter = Inter({ subsets: ["latin"] })

export const metadata: Metadata = {
  title: "Nails by Lingg - Premium Nail Care & Artistry",
  description:
    "Experience luxury nail care and artistry at Nails by Lingg. Book your appointment today for premium manicures, nail art, and spa treatments.",
    generator: 'v0.dev'
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en">
      <head>
        <link
          href="https://fonts.googleapis.com/css2?family=Dancing+Script:wght@400;500;600;700&display=swap"
          rel="stylesheet"
        />
        <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no" />
        <meta name="apple-mobile-web-app-capable" content="yes" />
        <meta name="apple-mobile-web-app-status-bar-style" content="default" />
        <meta name="theme-color" content="#ec4899" />
      </head>
      <body className={`${inter.className} overscroll-none`}>
        <AuthProvider>
          <div className="w-full max-w-md mx-auto bg-white min-h-screen relative md:max-w-full md:px-4 lg:px-8">
            <div className="md:max-w-4xl md:mx-auto">
              {children}
              <BottomNav />
            </div>
          </div>
          <Toaster position="top-center" richColors />
        </AuthProvider>
      </body>
    </html>
  )
}
