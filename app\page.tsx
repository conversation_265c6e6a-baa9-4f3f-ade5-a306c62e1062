import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Star, Calendar, Crown, Heart, Clock, ChevronRight, MapPin, Phone, User, Gift } from "lucide-react"
import { AppHeader } from "@/components/ui/app-header"
import { FloatingActionButton } from "@/components/ui/floating-action-button"
import { MyWorkGallery } from "@/components/my-work-gallery"
import { ImageUpload } from "@/components/image-upload"
import { HomepageStats } from "@/components/homepage-stats"
import { CustomerReviews } from "@/components/customer-reviews"

// Featured services for homepage
const featuredServices = [
  {
    name: "Gel Manicure",
    price: "$45",
    duration: "60 min",
    image: "/placeholder.svg?height=120&width=180&text=Gel+Manicure",
    popular: true,
  },
  {
    name: "Luxury Spa",
    price: "$65",
    duration: "75 min",
    image: "/placeholder.svg?height=120&width=180&text=Luxury+Spa",
  },
  { name: "Nail Art", price: "$75", duration: "90 min", image: "/placeholder.svg?height=120&width=180&text=Nail+Art" },
  {
    name: "Diamond Manicure",
    price: "$120",
    duration: "90 min",
    image: "/placeholder.svg?height=120&width=180&text=Diamond+Manicure",
    premium: true,
  },
]

// Quick stats
const quickStats = [
  { label: "Happy Clients", value: "5K+", icon: Heart },
  { label: "Years Experience", value: "8+", icon: Crown },
  { label: "Awards Won", value: "15+", icon: Star },
]

// Featured portfolio photos
const portfolioPhotos = [
  {
    url: "/placeholder.svg?height=150&width=150&text=Gel+Art",
    service: "Gel Manicure with Art",
  },
  {
    url: "/placeholder.svg?height=150&width=150&text=French+Tips",
    service: "French Manicure",
  },
  {
    url: "/placeholder.svg?height=150&width=150&text=Diamond+Luxury",
    service: "Diamond Manicure",
  },
  {
    url: "/placeholder.svg?height=150&width=150&text=3D+Art",
    service: "3D Nail Art",
  },
]

export default function HomePage() {
  return (
    <div className="min-h-screen bg-gray-50 pb-20">
      <AppHeader title="Nails by Lingg" showNotifications />

      {/* Hero Card */}
      <div className="p-4">
        <Card className="relative overflow-hidden bg-gradient-to-br from-pink-500 to-amber-500 border-0">
          <CardContent className="p-6 text-white">
            <div className="relative z-10">
              <h1 className="text-3xl font-bold mb-2" style={{ fontFamily: "Dancing Script, cursive" }}>
                Welcome Back!
              </h1>
              <p className="text-pink-100 mb-6">Ready for your next luxury nail experience?</p>
              <Button asChild className="bg-white text-pink-600 hover:bg-pink-50 rounded-full font-semibold">
                <Link href="/booking">
                  <Calendar className="mr-2 h-4 w-4" />
                  Book Appointment
                </Link>
              </Button>
            </div>
            <div className="absolute top-0 right-0 w-32 h-32 bg-white/10 rounded-full -mr-16 -mt-16" />
            <div className="absolute bottom-0 left-0 w-24 h-24 bg-white/10 rounded-full -ml-12 -mb-12" />
          </CardContent>
        </Card>
      </div>

      {/* Quick Stats */}
      <div className="px-4 mb-6">
        <HomepageStats />
      </div>

      {/* Quick Actions */}
      <div className="px-4 mb-6">
        <h2 className="text-xl font-bold text-gray-900 mb-4">Quick Actions</h2>
        <div className="grid grid-cols-2 gap-3">
          <Link href="/profile">
            <Card className="border-0 shadow-sm hover:shadow-md transition-shadow">
              <CardContent className="p-4 text-center">
                <div className="w-12 h-12 bg-pink-100 rounded-full flex items-center justify-center mx-auto mb-3">
                  <User className="h-6 w-6 text-pink-600" />
                </div>
                <h3 className="font-semibold text-gray-900 mb-1">My Profile</h3>
                <p className="text-xs text-gray-500">View photos & rewards</p>
              </CardContent>
            </Card>
          </Link>

          <Link href="/referrals">
            <Card className="border-0 shadow-sm hover:shadow-md transition-shadow">
              <CardContent className="p-4 text-center">
                <div className="w-12 h-12 bg-amber-100 rounded-full flex items-center justify-center mx-auto mb-3">
                  <Gift className="h-6 w-6 text-amber-600" />
                </div>
                <h3 className="font-semibold text-gray-900 mb-1">Refer Friends</h3>
                <p className="text-xs text-gray-500">Earn $10 per referral</p>
              </CardContent>
            </Card>
          </Link>
        </div>
      </div>

      {/* Popular Services */}
      <div className="px-4 mb-6">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-xl font-bold text-gray-900">Popular Services</h2>
          <Button variant="ghost" size="sm" asChild className="text-pink-600">
            <Link href="/services">
              View All
              <ChevronRight className="ml-1 h-4 w-4" />
            </Link>
          </Button>
        </div>

        <div className="space-y-3">
          {featuredServices.map((service, index) => (
            <Card key={index} className="border-0 shadow-sm">
              <CardContent className="p-0">
                <div className="flex items-center">
                  <img
                    src={service.image || "/placeholder.svg"}
                    alt={service.name}
                    className="w-20 h-20 object-cover rounded-l-lg"
                  />
                  <div className="flex-1 p-4">
                    <div className="flex items-center justify-between mb-1">
                      <h3 className="font-semibold text-gray-900">{service.name}</h3>
                      {service.popular && (
                        <span className="bg-pink-100 text-pink-600 text-xs px-2 py-1 rounded-full">Popular</span>
                      )}
                      {service.premium && (
                        <span className="bg-amber-100 text-amber-600 text-xs px-2 py-1 rounded-full">Premium</span>
                      )}
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-lg font-bold text-pink-600">{service.price}</span>
                      <span className="text-sm text-gray-500">{service.duration}</span>
                    </div>
                  </div>
                  <ChevronRight className="h-5 w-5 text-gray-400 mr-3" />
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>

      {/* My Work Gallery */}
      <div className="px-4 mb-6">
        <MyWorkGallery limit={4} />
      </div>

      {/* Recent Reviews */}
      <div className="px-4 mb-6">
        <CustomerReviews limit={3} featuredOnly={true} />
      </div>

      {/* Contact Info */}
      <div className="px-4 mb-6">
        <Card className="border-0 shadow-sm bg-gradient-to-r from-pink-50 to-amber-50">
          <CardContent className="p-4">
            <h3 className="font-semibold text-gray-900 mb-3">Visit Us Today</h3>
            <div className="space-y-2">
              <div className="flex items-center text-sm text-gray-600">
                <MapPin className="h-4 w-4 mr-2 text-pink-500" />
                123 Beauty Lane, Luxury City
              </div>
              <div className="flex items-center text-sm text-gray-600">
                <Phone className="h-4 w-4 mr-2 text-pink-500" />
                (555) 123-NAIL
              </div>
              <div className="flex items-center text-sm text-gray-600">
                <Clock className="h-4 w-4 mr-2 text-pink-500" />
                Mon-Sat 9AM-7PM
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <FloatingActionButton />
    </div>
  )
}
