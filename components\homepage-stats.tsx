"use client"

import { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Users, Calendar, Sparkles, Star, BarChart3 } from 'lucide-react'
import { supabase } from '@/lib/supabase'

interface HomepageStat {
  id: string
  title: string
  value: string
  description: string
  icon: string
  display_order: number
  active: boolean
}

const iconMap = {
  'users': Users,
  'calendar': Calendar,
  'sparkles': Sparkles,
  'star': Star,
  'bar-chart': BarChart3,
}

export function HomepageStats() {
  const [stats, setStats] = useState<HomepageStat[]>([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    fetchStats()
  }, [])

  const fetchStats = async () => {
    try {
      const { data, error } = await supabase
        .from('homepage_stats')
        .select('*')
        .eq('active', true)
        .order('display_order', { ascending: true })

      if (error) {
        console.error('Error fetching homepage stats:', error)
        return
      }

      setStats(data || [])
    } catch (error) {
      console.error('Error fetching homepage stats:', error)
    } finally {
      setLoading(false)
    }
  }

  const getIcon = (iconName: string) => {
    return iconMap[iconName as keyof typeof iconMap] || Users
  }

  if (loading) {
    return (
      <div className="grid grid-cols-2 gap-3">
        {[...Array(4)].map((_, i) => (
          <div key={i} className="h-24 bg-gray-200 rounded-lg animate-pulse" />
        ))}
      </div>
    )
  }

  if (stats.length === 0) {
    // Fallback to default stats if none are configured
    const defaultStats = [
      { icon: 'users', title: 'Happy Clients', value: '500+', description: 'Satisfied customers' },
      { icon: 'calendar', title: 'Years Experience', value: '8+', description: 'Professional service' },
      { icon: 'sparkles', title: 'Services', value: '15+', description: 'Beauty treatments' },
      { icon: 'star', title: 'Rating', value: '4.9★', description: 'Customer reviews' },
    ]

    return (
      <div className="grid grid-cols-2 gap-3">
        {defaultStats.map((stat, index) => {
          const IconComponent = getIcon(stat.icon)
          return (
            <Card key={index} className="border-pink-200 bg-white/80 backdrop-blur-sm">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium text-pink-800">{stat.title}</CardTitle>
                <IconComponent className="h-4 w-4 text-amber-500" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-pink-800">{stat.value}</div>
                <p className="text-xs text-pink-600">{stat.description}</p>
              </CardContent>
            </Card>
          )
        })}
      </div>
    )
  }

  return (
    <div className="grid grid-cols-2 gap-3">
      {stats.map((stat) => {
        const IconComponent = getIcon(stat.icon)
        return (
          <Card key={stat.id} className="border-pink-200 bg-white/80 backdrop-blur-sm">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-pink-800">{stat.title}</CardTitle>
              <IconComponent className="h-4 w-4 text-amber-500" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-pink-800">{stat.value}</div>
              <p className="text-xs text-pink-600">{stat.description}</p>
            </CardContent>
          </Card>
        )
      })}
    </div>
  )
}
