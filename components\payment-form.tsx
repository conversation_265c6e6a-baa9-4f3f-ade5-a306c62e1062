"use client"

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { CreditCard, Lock, Calendar, User } from 'lucide-react'

interface PaymentFormProps {
  total: number
  onPaymentSubmit: (paymentData: PaymentData) => void
  loading?: boolean
}

interface PaymentData {
  cardNumber: string
  expiryDate: string
  cvv: string
  cardholderName: string
  billingAddress: {
    street: string
    city: string
    state: string
    zipCode: string
  }
}

export function PaymentForm({ total, onPaymentSubmit, loading = false }: PaymentFormProps) {
  const [paymentData, setPaymentData] = useState<PaymentData>({
    cardNumber: '',
    expiryDate: '',
    cvv: '',
    cardholderName: '',
    billingAddress: {
      street: '',
      city: '',
      state: '',
      zipCode: ''
    }
  })

  const formatCardNumber = (value: string) => {
    // Remove all non-digits
    const digits = value.replace(/\D/g, '')
    
    // Add spaces every 4 digits
    const formatted = digits.replace(/(\d{4})(?=\d)/g, '$1 ')
    
    return formatted.slice(0, 19) // Limit to 16 digits + 3 spaces
  }

  const formatExpiryDate = (value: string) => {
    // Remove all non-digits
    const digits = value.replace(/\D/g, '')
    
    // Add slash after 2 digits
    if (digits.length >= 2) {
      return `${digits.slice(0, 2)}/${digits.slice(2, 4)}`
    }
    
    return digits
  }

  const validateCardNumber = (cardNumber: string) => {
    const digits = cardNumber.replace(/\D/g, '')
    return digits.length === 16
  }

  const validateExpiryDate = (expiryDate: string) => {
    const [month, year] = expiryDate.split('/')
    if (!month || !year || month.length !== 2 || year.length !== 2) {
      return false
    }
    
    const monthNum = parseInt(month)
    const yearNum = parseInt(`20${year}`)
    const currentDate = new Date()
    const currentYear = currentDate.getFullYear()
    const currentMonth = currentDate.getMonth() + 1
    
    if (monthNum < 1 || monthNum > 12) {
      return false
    }
    
    if (yearNum < currentYear || (yearNum === currentYear && monthNum < currentMonth)) {
      return false
    }
    
    return true
  }

  const validateCVV = (cvv: string) => {
    return cvv.length === 3 || cvv.length === 4
  }

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!validateCardNumber(paymentData.cardNumber)) {
      alert('Please enter a valid 16-digit card number')
      return
    }
    
    if (!validateExpiryDate(paymentData.expiryDate)) {
      alert('Please enter a valid expiry date (MM/YY)')
      return
    }
    
    if (!validateCVV(paymentData.cvv)) {
      alert('Please enter a valid CVV (3-4 digits)')
      return
    }
    
    if (!paymentData.cardholderName.trim()) {
      alert('Please enter the cardholder name')
      return
    }
    
    onPaymentSubmit(paymentData)
  }

  const handleCardNumberChange = (value: string) => {
    const formatted = formatCardNumber(value)
    setPaymentData({ ...paymentData, cardNumber: formatted })
  }

  const handleExpiryDateChange = (value: string) => {
    const formatted = formatExpiryDate(value)
    setPaymentData({ ...paymentData, expiryDate: formatted })
  }

  const handleCVVChange = (value: string) => {
    const digits = value.replace(/\D/g, '').slice(0, 4)
    setPaymentData({ ...paymentData, cvv: digits })
  }

  return (
    <Card className="border-pink-200">
      <CardHeader>
        <CardTitle className="text-xl font-semibold text-pink-800 flex items-center">
          <Lock className="h-5 w-5 mr-2" />
          Secure Payment
        </CardTitle>
        <div className="flex items-center text-sm text-gray-600">
          <Lock className="h-4 w-4 mr-1" />
          Your payment information is encrypted and secure
        </div>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Payment Amount */}
          <div className="bg-pink-50 p-4 rounded-lg border border-pink-200">
            <div className="flex justify-between items-center">
              <span className="text-pink-700 font-medium">Total Amount:</span>
              <span className="text-2xl font-bold text-pink-800">${total.toFixed(2)}</span>
            </div>
          </div>

          {/* Card Information */}
          <div className="space-y-4">
            <h3 className="font-semibold text-pink-800">Card Information</h3>
            
            <div>
              <Label htmlFor="cardNumber" className="text-pink-800">Card Number</Label>
              <div className="relative">
                <CreditCard className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <Input
                  id="cardNumber"
                  value={paymentData.cardNumber}
                  onChange={(e) => handleCardNumberChange(e.target.value)}
                  className="pl-10 border-pink-200 focus:border-pink-500"
                  placeholder="1234 5678 9012 3456"
                  required
                />
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="expiryDate" className="text-pink-800">Expiry Date</Label>
                <div className="relative">
                  <Calendar className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <Input
                    id="expiryDate"
                    value={paymentData.expiryDate}
                    onChange={(e) => handleExpiryDateChange(e.target.value)}
                    className="pl-10 border-pink-200 focus:border-pink-500"
                    placeholder="MM/YY"
                    required
                  />
                </div>
              </div>
              <div>
                <Label htmlFor="cvv" className="text-pink-800">CVV</Label>
                <Input
                  id="cvv"
                  value={paymentData.cvv}
                  onChange={(e) => handleCVVChange(e.target.value)}
                  className="border-pink-200 focus:border-pink-500"
                  placeholder="123"
                  required
                />
              </div>
            </div>

            <div>
              <Label htmlFor="cardholderName" className="text-pink-800">Cardholder Name</Label>
              <div className="relative">
                <User className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <Input
                  id="cardholderName"
                  value={paymentData.cardholderName}
                  onChange={(e) => setPaymentData({ ...paymentData, cardholderName: e.target.value })}
                  className="pl-10 border-pink-200 focus:border-pink-500"
                  placeholder="John Doe"
                  required
                />
              </div>
            </div>
          </div>

          {/* Billing Address */}
          <div className="space-y-4">
            <h3 className="font-semibold text-pink-800">Billing Address</h3>
            
            <div>
              <Label htmlFor="street" className="text-pink-800">Street Address</Label>
              <Input
                id="street"
                value={paymentData.billingAddress.street}
                onChange={(e) => setPaymentData({
                  ...paymentData,
                  billingAddress: { ...paymentData.billingAddress, street: e.target.value }
                })}
                className="border-pink-200 focus:border-pink-500"
                placeholder="123 Main Street"
                required
              />
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="city" className="text-pink-800">City</Label>
                <Input
                  id="city"
                  value={paymentData.billingAddress.city}
                  onChange={(e) => setPaymentData({
                    ...paymentData,
                    billingAddress: { ...paymentData.billingAddress, city: e.target.value }
                  })}
                  className="border-pink-200 focus:border-pink-500"
                  placeholder="New York"
                  required
                />
              </div>
              <div>
                <Label htmlFor="state" className="text-pink-800">State</Label>
                <Select
                  value={paymentData.billingAddress.state}
                  onValueChange={(value) => setPaymentData({
                    ...paymentData,
                    billingAddress: { ...paymentData.billingAddress, state: value }
                  })}
                >
                  <SelectTrigger className="border-pink-200">
                    <SelectValue placeholder="Select state" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="AL">Alabama</SelectItem>
                    <SelectItem value="AK">Alaska</SelectItem>
                    <SelectItem value="AZ">Arizona</SelectItem>
                    <SelectItem value="AR">Arkansas</SelectItem>
                    <SelectItem value="CA">California</SelectItem>
                    <SelectItem value="CO">Colorado</SelectItem>
                    <SelectItem value="CT">Connecticut</SelectItem>
                    <SelectItem value="DE">Delaware</SelectItem>
                    <SelectItem value="FL">Florida</SelectItem>
                    <SelectItem value="GA">Georgia</SelectItem>
                    <SelectItem value="HI">Hawaii</SelectItem>
                    <SelectItem value="ID">Idaho</SelectItem>
                    <SelectItem value="IL">Illinois</SelectItem>
                    <SelectItem value="IN">Indiana</SelectItem>
                    <SelectItem value="IA">Iowa</SelectItem>
                    <SelectItem value="KS">Kansas</SelectItem>
                    <SelectItem value="KY">Kentucky</SelectItem>
                    <SelectItem value="LA">Louisiana</SelectItem>
                    <SelectItem value="ME">Maine</SelectItem>
                    <SelectItem value="MD">Maryland</SelectItem>
                    <SelectItem value="MA">Massachusetts</SelectItem>
                    <SelectItem value="MI">Michigan</SelectItem>
                    <SelectItem value="MN">Minnesota</SelectItem>
                    <SelectItem value="MS">Mississippi</SelectItem>
                    <SelectItem value="MO">Missouri</SelectItem>
                    <SelectItem value="MT">Montana</SelectItem>
                    <SelectItem value="NE">Nebraska</SelectItem>
                    <SelectItem value="NV">Nevada</SelectItem>
                    <SelectItem value="NH">New Hampshire</SelectItem>
                    <SelectItem value="NJ">New Jersey</SelectItem>
                    <SelectItem value="NM">New Mexico</SelectItem>
                    <SelectItem value="NY">New York</SelectItem>
                    <SelectItem value="NC">North Carolina</SelectItem>
                    <SelectItem value="ND">North Dakota</SelectItem>
                    <SelectItem value="OH">Ohio</SelectItem>
                    <SelectItem value="OK">Oklahoma</SelectItem>
                    <SelectItem value="OR">Oregon</SelectItem>
                    <SelectItem value="PA">Pennsylvania</SelectItem>
                    <SelectItem value="RI">Rhode Island</SelectItem>
                    <SelectItem value="SC">South Carolina</SelectItem>
                    <SelectItem value="SD">South Dakota</SelectItem>
                    <SelectItem value="TN">Tennessee</SelectItem>
                    <SelectItem value="TX">Texas</SelectItem>
                    <SelectItem value="UT">Utah</SelectItem>
                    <SelectItem value="VT">Vermont</SelectItem>
                    <SelectItem value="VA">Virginia</SelectItem>
                    <SelectItem value="WA">Washington</SelectItem>
                    <SelectItem value="WV">West Virginia</SelectItem>
                    <SelectItem value="WI">Wisconsin</SelectItem>
                    <SelectItem value="WY">Wyoming</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div>
              <Label htmlFor="zipCode" className="text-pink-800">ZIP Code</Label>
              <Input
                id="zipCode"
                value={paymentData.billingAddress.zipCode}
                onChange={(e) => setPaymentData({
                  ...paymentData,
                  billingAddress: { ...paymentData.billingAddress, zipCode: e.target.value }
                })}
                className="border-pink-200 focus:border-pink-500"
                placeholder="12345"
                required
              />
            </div>
          </div>

          <Button
            type="submit"
            disabled={loading}
            className="w-full bg-gradient-to-r from-pink-500 to-amber-500 hover:from-pink-600 hover:to-amber-600"
          >
            {loading ? (
              <div className="flex items-center">
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                Processing Payment...
              </div>
            ) : (
              <>
                <CreditCard className="mr-2 h-4 w-4" />
                Pay ${total.toFixed(2)}
              </>
            )}
          </Button>

          <div className="text-xs text-gray-500 text-center">
            <Lock className="h-3 w-3 inline mr-1" />
            Your payment is secured with 256-bit SSL encryption
          </div>
        </form>
      </CardContent>
    </Card>
  )
}
